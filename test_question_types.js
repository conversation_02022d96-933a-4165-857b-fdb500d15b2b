require('dotenv').config();
const OpenAI = require('openai');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

console.log('OpenAI API Key:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');

// Test specific question types that are problematic
async function testSpecificQuestionTypes() {
  console.log('🧮 Testing Specific Mathematics Question Types\n');
  
  const testCases = [
    {
      name: "Measurement Conversion Questions",
      prompt: `Generate 3 measurement conversion questions for Level1 assessment.

Examples needed:
- How many millilitres are in 1 litre?
- Convert 150cm to metres
- How many grams in 2.5 kilograms?

Return JSON array with proper question types and required fields:
[{"id":1,"type":"multiple-choice","topic":"measurement","question":"How many millilitres are in 1 litre?","options":["100ml","500ml","1000ml","1500ml"],"correctAnswer":"1000ml","points":2}]

Generate exactly 3 measurement questions:`
    },
    {
      name: "Fraction Questions", 
      prompt: `Generate 3 fraction questions for Level1 assessment.

Examples needed:
- What is 1/2 + 1/4?
- Simplify 6/8
- What is 3/4 of 20?

Return JSON array with proper question types and required fields:
[{"id":1,"type":"multiple-choice","topic":"fractions","question":"What is 1/2 + 1/4?","options":["1/6","2/6","3/4","1/8"],"correctAnswer":"3/4","points":2}]

Generate exactly 3 fraction questions:`
    },
    {
      name: "Mixed Question Types",
      prompt: `Generate 4 questions for Level1 assessment with different question types:

1. Multiple choice measurement question
2. Numeric fraction calculation  
3. Multiple choice percentage question
4. Numeric algebra question

Required format:
- multiple-choice questions MUST have "options" array with 4 choices
- numeric questions MUST have numerical "correctAnswer" 
- All questions MUST have: id, type, topic, question, correctAnswer, points

Return JSON array:
[{"id":1,"type":"multiple-choice","topic":"measurement","question":"How many cm in 1 metre?","options":["10cm","50cm","100cm","1000cm"],"correctAnswer":"100cm","points":2}]

Generate exactly 4 mixed questions:`
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== ${testCase.name} ===`);
    
    try {
      const startTime = Date.now();
      
      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are a mathematics assessment expert. Generate questions in valid JSON format with correct question types and all required fields."
          },
          {
            role: "user",
            content: testCase.prompt
          }
        ],
        max_tokens: 1200,
        temperature: 0.1
      });
      
      const duration = Date.now() - startTime;
      console.log(`✅ Generated in ${duration}ms`);
      
      const responseText = completion.choices[0].message.content;
      console.log('Raw response length:', responseText.length);
      console.log('First 200 chars:', responseText.substring(0, 200));
      
      // Try to parse and validate
      try {
        // Clean markdown if present
        let cleanedText = responseText;
        cleanedText = cleanedText.replace(/```json\s*/g, '').replace(/```\s*/g, '').replace(/`/g, '');
        
        // Try to extract JSON array
        const jsonMatch = cleanedText.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          const questions = JSON.parse(jsonMatch[0]);
          
          console.log(`✅ Parsed ${questions.length} questions`);
          
          // Validate each question
          questions.forEach((q, index) => {
            console.log(`\nQuestion ${index + 1}:`);
            console.log(`- Type: ${q.type || 'MISSING'}`);
            console.log(`- Topic: ${q.topic || 'MISSING'}`);
            console.log(`- Question: ${q.question ? q.question.substring(0, 50) + '...' : 'MISSING'}`);
            console.log(`- Has options: ${q.options ? 'YES (' + q.options.length + ')' : 'NO'}`);
            console.log(`- Correct answer: ${q.correctAnswer || 'MISSING'}`);
            console.log(`- Points: ${q.points || 'MISSING'}`);
            
            // Check for issues
            const issues = [];
            if (!q.type) issues.push('Missing type');
            if (!q.topic) issues.push('Missing topic');
            if (!q.question) issues.push('Missing question');
            if (!q.correctAnswer) issues.push('Missing correctAnswer');
            if (!q.points) issues.push('Missing points');
            
            if (q.type === 'multiple-choice' || q.type === 'multiple_choice') {
              if (!q.options || !Array.isArray(q.options)) {
                issues.push('Missing/invalid options array');
              } else if (q.options.length !== 4) {
                issues.push(`Wrong number of options (${q.options.length}, expected 4)`);
              }
            }
            
            if (issues.length > 0) {
              console.log(`❌ Issues: ${issues.join(', ')}`);
            } else {
              console.log(`✅ Question valid`);
            }
          });
          
        } else {
          console.log('❌ Could not extract JSON array from response');
        }
        
      } catch (parseError) {
        console.log('❌ JSON parsing failed:', parseError.message);
        console.log('Raw response:', responseText);
      }
      
    } catch (error) {
      console.log(`❌ API call failed: ${error.message}`);
    }
  }
}

// Test frontend compatibility
function testFrontendCompatibility() {
  console.log('\n=== Frontend Compatibility Test ===');
  
  const sampleQuestions = [
    {
      id: 1,
      type: "multiple-choice", // Frontend expects hyphen
      topic: "measurement",
      question: "How many millilitres are in 1 litre?",
      options: ["100ml", "500ml", "1000ml", "1500ml"],
      correctAnswer: "1000ml",
      points: 2
    },
    {
      id: 2,
      type: "multiple_choice", // AI often generates underscore
      topic: "measurement", 
      question: "How many millilitres are in 1 litre?",
      options: ["100ml", "500ml", "1000ml", "1500ml"],
      correctAnswer: "1000ml",
      points: 2
    },
    {
      id: 3,
      type: "numeric",
      topic: "fractions",
      question: "What is 1/2 + 1/4? (Enter as decimal)",
      correctAnswer: "0.75",
      points: 2
    },
    {
      id: 4,
      type: "text", // AI generates this
      topic: "algebra",
      question: "Solve for x: 2x + 5 = 11",
      correctAnswer: "3",
      points: 2
    },
    {
      id: 5,
      type: "short-answer", // Frontend expects this
      topic: "algebra",
      question: "Solve for x: 2x + 5 = 11", 
      correctAnswer: "3",
      points: 2
    }
  ];
  
  console.log('Testing question type compatibility:');
  
  sampleQuestions.forEach(q => {
    console.log(`\nQuestion ${q.id}:`);
    console.log(`- Type: "${q.type}"`);
    
    // Simulate frontend rendering logic
    if (q.type === 'multiple-choice') {
      if (q.options && Array.isArray(q.options)) {
        console.log(`✅ Frontend: Would render multiple choice with ${q.options.length} options`);
      } else {
        console.log(`❌ Frontend: multiple-choice type but no options array`);
      }
    } else if (q.type === 'multiple_choice') {
      console.log(`❌ Frontend: Unrecognized type "multiple_choice" (expects "multiple-choice")`);
    } else if (q.type === 'numeric') {
      console.log(`✅ Frontend: Would render numeric input`);
    } else if (q.type === 'short-answer') {
      console.log(`✅ Frontend: Would render short answer input`);
    } else if (q.type === 'text') {
      console.log(`❌ Frontend: Unrecognized type "text" (expects "short-answer")`);
    } else {
      console.log(`❌ Frontend: Unknown question type "${q.type}"`);
    }
  });
}

// Run all tests
async function runAllTests() {
  console.log('🔧 Mathematics Question Type Testing\n');
  
  await testSpecificQuestionTypes();
  testFrontendCompatibility();
  
  console.log('\n🎉 Testing completed!');
}

runAllTests().catch(console.error);
