<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematics Assessment UI Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .container-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .container-item {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .visible {
            background: #d1fae5;
            color: #065f46;
        }
        .hidden {
            background: #fee2e2;
            color: #991b1b;
        }
        .not-found {
            background: #f3f4f6;
            color: #6b7280;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Mathematics Assessment UI Flow Test</h1>
    
    <div class="test-section">
        <h2>Container Visibility Test</h2>
        <p>This test verifies that only one container is visible at a time during the assessment flow.</p>
        
        <button class="test-button" onclick="openMathAssessment()">Open Math Assessment</button>
        <button class="test-button" onclick="testContainerFlow()">Test Container Flow</button>
        <button class="test-button" onclick="checkCurrentState()">Check Current State</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Live Assessment Preview</h2>
        <p>Preview the mathematics assessment interface to verify the UI flow works correctly.</p>
        
        <iframe id="assessment-frame" src="/math.html"></iframe>
    </div>

    <div class="test-section">
        <h2>Manual Test Instructions</h2>
        <ol>
            <li><strong>Initial Load:</strong> Only the user form should be visible</li>
            <li><strong>Form Submission:</strong> User form should hide, instructions should appear</li>
            <li><strong>Begin Assessment:</strong> Instructions should hide, questions should appear</li>
            <li><strong>Complete Assessment:</strong> Questions should hide, results should appear</li>
            <li><strong>Progression/Retake:</strong> Results should hide, instructions should reappear</li>
        </ol>
        
        <h3>Expected Container States:</h3>
        <div class="container-status">
            <div class="container-item">
                <strong>Step 1 - Initial:</strong><br>
                user-form-container: ✅ Visible<br>
                math-assessment-container: ❌ Hidden
            </div>
            <div class="container-item">
                <strong>Step 2 - Instructions:</strong><br>
                user-form-container: ❌ Hidden<br>
                assessment-instructions: ✅ Visible
            </div>
            <div class="container-item">
                <strong>Step 3 - Questions:</strong><br>
                assessment-instructions: ❌ Hidden<br>
                assessment-questions: ✅ Visible
            </div>
            <div class="container-item">
                <strong>Step 4 - Results:</strong><br>
                assessment-questions: ❌ Hidden<br>
                assessment-results: ✅ Visible
            </div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const container = document.getElementById('test-results');
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
            
            // Auto-scroll to latest result
            result.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function openMathAssessment() {
            window.open('/math.html', '_blank');
        }

        function getContainerVisibility() {
            const frame = document.getElementById('assessment-frame');
            const frameDoc = frame.contentDocument || frame.contentWindow.document;
            
            const containers = [
                'user-form-container',
                'math-assessment-container',
                'assessment-instructions',
                'assessment-questions',
                'assessment-results',
                'assessment-loading',
                'header'
            ];
            
            const state = {};
            containers.forEach(id => {
                const element = frameDoc.getElementById(id);
                if (!element) {
                    state[id] = 'not-found';
                } else {
                    state[id] = element.classList.contains('hidden') ? 'hidden' : 'visible';
                }
            });
            
            return state;
        }

        function displayContainerState(state, title) {
            let html = `<strong>${title}</strong><div class="container-status">`;
            
            Object.entries(state).forEach(([id, status]) => {
                const className = status === 'visible' ? 'visible' : 
                                status === 'hidden' ? 'hidden' : 'not-found';
                html += `<div class="container-item ${className}">${id}: ${status}</div>`;
            });
            
            html += '</div>';
            return html;
        }

        function checkCurrentState() {
            try {
                const state = getContainerVisibility();
                const visibleContainers = Object.entries(state)
                    .filter(([id, status]) => status === 'visible')
                    .map(([id]) => id);
                
                addResult(displayContainerState(state, 'Current Container State'), 'info');
                
                if (visibleContainers.length === 0) {
                    addResult('⚠️ No containers are visible!', 'warning');
                } else if (visibleContainers.length === 1) {
                    addResult(`✅ Correct: Only one container visible (${visibleContainers[0]})`, 'success');
                } else {
                    addResult(`❌ Error: Multiple containers visible (${visibleContainers.join(', ')})`, 'error');
                }
            } catch (error) {
                addResult(`❌ Error checking state: ${error.message}`, 'error');
            }
        }

        function testContainerFlow() {
            addResult('🧪 Starting container flow test...', 'info');
            
            try {
                const frame = document.getElementById('assessment-frame');
                const frameWindow = frame.contentWindow;
                
                if (!frameWindow.mathAssessment) {
                    addResult('❌ MathAssessment not found in iframe', 'error');
                    return;
                }
                
                // Test 1: Initial state
                let state = getContainerVisibility();
                addResult(displayContainerState(state, 'Test 1: Initial State'), 'info');
                
                // Test 2: Reset to initial state
                frameWindow.mathAssessment.resetToInitialState();
                setTimeout(() => {
                    state = getContainerVisibility();
                    addResult(displayContainerState(state, 'Test 2: After resetToInitialState()'), 'info');
                    
                    // Test 3: Show instructions
                    frameWindow.mathAssessment.currentLevel = 'Entry';
                    frameWindow.mathAssessment.showInstructions();
                    setTimeout(() => {
                        state = getContainerVisibility();
                        addResult(displayContainerState(state, 'Test 3: After showInstructions()'), 'info');
                        
                        // Test 4: Show loading
                        frameWindow.mathAssessment.showLoading('Testing...');
                        setTimeout(() => {
                            state = getContainerVisibility();
                            addResult(displayContainerState(state, 'Test 4: After showLoading()'), 'info');
                            
                            // Test 5: Show questions
                            frameWindow.mathAssessment.hideLoading();
                            frameWindow.mathAssessment.showQuestions();
                            setTimeout(() => {
                                state = getContainerVisibility();
                                addResult(displayContainerState(state, 'Test 5: After showQuestions()'), 'info');
                                
                                // Test 6: Reset back
                                frameWindow.mathAssessment.resetToInitialState();
                                setTimeout(() => {
                                    state = getContainerVisibility();
                                    addResult(displayContainerState(state, 'Test 6: Final reset'), 'info');
                                    addResult('✅ Container flow test completed!', 'success');
                                }, 100);
                            }, 100);
                        }, 100);
                    }, 100);
                }, 100);
            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        // Auto-check state when iframe loads
        document.getElementById('assessment-frame').addEventListener('load', function() {
            setTimeout(() => {
                addResult('📋 Assessment iframe loaded. Checking initial state...', 'info');
                checkCurrentState();
            }, 1000);
        });

        // Initial message
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 UI Flow Test Suite Ready', 'info');
            addResult('Use the buttons above to test the mathematics assessment UI flow', 'info');
        });
    </script>
</body>
</html>
