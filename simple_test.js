const http = require('http');

// Simple test to check if server is running and test mathematics assessment
function testMathAssessment() {
  const postData = JSON.stringify({
    email: '<EMAIL>',
    level: 'Level1',
    studentLevel: 'adult-learner'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/math-assessments/start',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('Testing mathematics assessment API...');
  console.log('Making request to:', `http://${options.hostname}:${options.port}${options.path}`);

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('✅ Success! Response received:');
        console.log('Assessment ID:', response.assessmentId);
        console.log('Question count:', response.questions?.length || 'N/A');
        console.log('Level:', response.level);
        
        if (response.questions && response.questions.length > 0) {
          console.log('\nFirst question:');
          console.log('Question:', response.questions[0].question);
          console.log('Topic:', response.questions[0].topic || 'N/A');
          console.log('Has explanation:', !!response.questions[0].explanation);
          
          // Check if this looks like AI-generated content
          const hasVariedContent = response.questions.some(q => 
            q.topic && q.explanation && q.question.length > 20
          );
          
          if (hasVariedContent) {
            console.log('✅ Questions appear to be AI-generated');
          } else {
            console.log('⚠️  Questions appear to be fallback/hardcoded');
          }
        }
        
      } catch (error) {
        console.error('❌ Failed to parse response:', error.message);
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request failed:', error.message);
  });

  req.on('timeout', () => {
    console.error('❌ Request timed out');
    req.destroy();
  });

  req.setTimeout(30000); // 30 second timeout
  req.write(postData);
  req.end();
}

// Test server connectivity first
function testServerConnectivity() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/math-assessments/performance',
    method: 'GET'
  };

  console.log('Testing server connectivity...');

  const req = http.request(options, (res) => {
    console.log(`✅ Server is accessible (Status: ${res.statusCode})`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const metrics = JSON.parse(data);
        console.log('Performance metrics:', metrics.metrics);
        
        // Now test the mathematics assessment
        setTimeout(testMathAssessment, 1000);
        
      } catch (error) {
        console.error('Failed to parse metrics:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Server connectivity test failed:', error.message);
    console.log('Make sure the server is running with: node server.js');
  });

  req.setTimeout(10000);
  req.end();
}

console.log('🧮 Simple Mathematics Assessment Test\n');
testServerConnectivity();
