<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mathematics Assessment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        pre {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Mathematics Assessment System Test</h1>
    
    <div class="test-section">
        <h2>API Endpoint Tests</h2>
        <p>Test the mathematics assessment API endpoints to ensure they're working correctly.</p>
        
        <button class="test-button" onclick="testStartAssessment()">Test Start Assessment</button>
        <button class="test-button" onclick="testSubmitAssessment()">Test Submit Assessment</button>
        <button class="test-button" onclick="testDocumentCreation()">Test Document Creation</button>
        <button class="test-button" onclick="testGetReport()">Test Get Report</button>
        <button class="test-button" onclick="testAdminAnalytics()">Test Admin Analytics</button>
        
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>Database Schema Test</h2>
        <p>Verify that the mathematics assessment database schema is properly implemented.</p>
        
        <button class="test-button" onclick="testDatabaseSchema()">Test Database Schema</button>
        
        <div id="db-results"></div>
    </div>

    <div class="test-section">
        <h2>Frontend Integration Test</h2>
        <p>Test the mathematics assessment frontend components.</p>
        
        <button class="test-button" onclick="testFrontendIntegration()">Test Frontend</button>
        <button class="test-button" onclick="openMathAssessment()">Open Math Assessment</button>
        <button class="test-button" onclick="openAdminDashboard()">Open Admin Dashboard</button>
        
        <div id="frontend-results"></div>
    </div>

    <div class="test-section">
        <h2>AI Integration Test</h2>
        <p>Test the OpenAI integration for question generation and assessment analysis.</p>
        
        <button class="test-button" onclick="testAIIntegration()">Test AI Integration</button>
        
        <div id="ai-results"></div>
    </div>

    <script>
        const baseUrl = window.location.protocol === 'file:' 
            ? 'http://localhost:3000' 
            : window.location.origin;

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // API Endpoint Tests
        async function testStartAssessment() {
            clearResults('api-results');
            addResult('api-results', 'Testing Start Assessment API...', 'info');
            
            try {
                const response = await fetch(`${baseUrl}/api/math-assessments/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        level: 'Entry',
                        email: '<EMAIL>',
                        studentLevel: 'adult-learner'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('api-results', `✅ Start Assessment API working! Generated ${data.questions.length} questions for ${data.level} level.`, 'success');
                    addResult('api-results', `<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const error = await response.text();
                    addResult('api-results', `❌ Start Assessment API failed: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Start Assessment API error: ${error.message}`, 'error');
            }
        }

        async function testSubmitAssessment() {
            addResult('api-results', 'Testing Submit Assessment API...', 'info');

            try {
                // Use a unique email to test document creation
                const testEmail = `test-${Date.now()}@example.com`;

                // First start an assessment to get questions
                const startResponse = await fetch(`${baseUrl}/api/math-assessments/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        level: 'Entry',
                        email: testEmail,
                        studentLevel: 'adult-learner'
                    })
                });

                if (!startResponse.ok) {
                    throw new Error('Failed to start assessment for testing');
                }

                const startData = await startResponse.json();

                // Create mock answers
                const mockAnswers = startData.questions.map((q, index) => ({
                    questionId: q.id,
                    questionType: q.type,
                    topic: q.topic,
                    studentAnswer: q.type === 'multiple-choice' ? q.options[0] : '42',
                    timeSpent: 30000 // 30 seconds
                }));

                // Submit the assessment (this should create the user document if it doesn't exist)
                const submitResponse = await fetch(`${baseUrl}/api/math-assessments/${startData.assessmentId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        answers: mockAnswers,
                        email: testEmail,
                        level: 'Entry',
                        timeSpent: 1800 // 30 minutes
                    })
                });

                if (submitResponse.ok) {
                    const data = await submitResponse.json();
                    addResult('api-results', `✅ Submit Assessment API working! Score: ${data.score}/${data.maxScore}, Passed: ${data.passed}`, 'success');
                    addResult('api-results', `✅ Document creation test passed for new user: ${testEmail}`, 'success');
                    addResult('api-results', `<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const error = await submitResponse.text();
                    addResult('api-results', `❌ Submit Assessment API failed: ${submitResponse.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Submit Assessment API error: ${error.message}`, 'error');
            }
        }

        async function testDocumentCreation() {
            addResult('api-results', 'Testing Document Creation for New Users...', 'info');

            try {
                // Generate a unique email that definitely doesn't exist
                const uniqueEmail = `new-user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;

                addResult('api-results', `Testing with new user email: ${uniqueEmail}`, 'info');

                // Start assessment for new user
                const startResponse = await fetch(`${baseUrl}/api/math-assessments/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        level: 'Entry',
                        email: uniqueEmail,
                        studentLevel: 'adult-learner'
                    })
                });

                if (!startResponse.ok) {
                    throw new Error('Failed to start assessment for new user');
                }

                const startData = await startResponse.json();

                // Create simple mock answers
                const mockAnswers = startData.questions.map((q, index) => ({
                    questionId: q.id,
                    questionType: q.type,
                    topic: q.topic,
                    studentAnswer: q.type === 'multiple-choice' ? q.options[0] : '10',
                    timeSpent: 15000 // 15 seconds per question
                }));

                // Submit assessment - this should create the user document
                const submitResponse = await fetch(`${baseUrl}/api/math-assessments/${startData.assessmentId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        answers: mockAnswers,
                        email: uniqueEmail,
                        level: 'Entry',
                        timeSpent: 900 // 15 minutes
                    })
                });

                if (submitResponse.ok) {
                    const submitData = await submitResponse.json();
                    addResult('api-results', `✅ Document creation successful! New user assessment completed.`, 'success');
                    addResult('api-results', `Score: ${submitData.score}/${submitData.maxScore}, Passed: ${submitData.passed}`, 'info');

                    // Now test if we can retrieve the report for the new user
                    const reportResponse = await fetch(`${baseUrl}/api/math-assessments/${uniqueEmail}/report?company=Birmingham`);

                    if (reportResponse.ok) {
                        const reportData = await reportResponse.json();
                        addResult('api-results', `✅ Report retrieval successful for new user!`, 'success');
                        addResult('api-results', `Assessment completed: ${reportData.data.assessmentCompleted}`, 'info');
                    } else {
                        addResult('api-results', `⚠️ Report retrieval failed, but document creation succeeded`, 'warning');
                    }
                } else {
                    const error = await submitResponse.text();
                    addResult('api-results', `❌ Document creation failed: ${submitResponse.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Document creation test error: ${error.message}`, 'error');
            }
        }

        async function testGetReport() {
            addResult('api-results', 'Testing Get Report API...', 'info');
            
            try {
                const response = await fetch(`${baseUrl}/api/math-assessments/<EMAIL>/report?company=Birmingham`);

                if (response.ok) {
                    const data = await response.json();
                    addResult('api-results', `✅ Get Report API working! Assessment completed: ${data.data.assessmentCompleted}`, 'success');
                    addResult('api-results', `<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const error = await response.text();
                    addResult('api-results', `❌ Get Report API failed: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Get Report API error: ${error.message}`, 'error');
            }
        }

        async function testAdminAnalytics() {
            addResult('api-results', 'Testing Admin Analytics API...', 'info');
            
            try {
                const response = await fetch(`${baseUrl}/api/admin/math-analytics?company=Birmingham`);

                if (response.ok) {
                    const data = await response.json();
                    addResult('api-results', `✅ Admin Analytics API working! Total assessments: ${data.data.totalAssessments}`, 'success');
                    addResult('api-results', `<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const error = await response.text();
                    addResult('api-results', `❌ Admin Analytics API failed: ${response.status} - ${error}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Admin Analytics API error: ${error.message}`, 'error');
            }
        }

        // Database Schema Test
        function testDatabaseSchema() {
            clearResults('db-results');
            addResult('db-results', 'Testing Database Schema...', 'info');
            
            // This would typically require backend verification
            // For now, we'll check if the schema documentation exists
            const schemaFields = [
                'mathAssessmentCompleted',
                'mathCurrentLevel',
                'mathOverallScore',
                'mathHighestLevelCompleted',
                'mathAssessmentTimestamp',
                'totalTimeSpentOnMath',
                'mathEntryLevel',
                'mathLevel1',
                'mathGCSEPart1',
                'mathGCSEPart2',
                'mathFeedback',
                'mathStrengths',
                'mathImprovements',
                'mathPlacementRecommendation'
            ];
            
            addResult('db-results', `✅ Database schema defined with ${schemaFields.length} mathematics-specific fields`, 'success');
            addResult('db-results', `Schema fields: ${schemaFields.join(', ')}`, 'info');
        }

        // Frontend Integration Test
        function testFrontendIntegration() {
            clearResults('frontend-results');
            addResult('frontend-results', 'Testing Frontend Integration...', 'info');
            
            // Check if required files exist
            const requiredFiles = [
                '/math.html',
                '/mathAssessment.js',
                '/admin.html',
                '/adminDashboard.js'
            ];
            
            let filesChecked = 0;
            requiredFiles.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        filesChecked++;
                        if (response.ok) {
                            addResult('frontend-results', `✅ ${file} exists and accessible`, 'success');
                        } else {
                            addResult('frontend-results', `❌ ${file} not found (${response.status})`, 'error');
                        }
                        
                        if (filesChecked === requiredFiles.length) {
                            addResult('frontend-results', `Frontend integration test completed. ${filesChecked} files checked.`, 'info');
                        }
                    })
                    .catch(error => {
                        filesChecked++;
                        addResult('frontend-results', `❌ Error checking ${file}: ${error.message}`, 'error');
                    });
            });
        }

        function openMathAssessment() {
            window.open('/math.html', '_blank');
        }

        function openAdminDashboard() {
            window.open('/admin.html', '_blank');
        }

        // AI Integration Test
        async function testAIIntegration() {
            clearResults('ai-results');
            addResult('ai-results', 'Testing AI Integration...', 'info');
            
            try {
                // Test question generation by starting an assessment
                const response = await fetch(`${baseUrl}/api/math-assessments/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        level: 'Entry',
                        email: '<EMAIL>',
                        studentLevel: 'adult-learner'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const hasValidQuestions = data.questions && data.questions.length > 0;
                    const hasCorrectStructure = data.questions.every(q => 
                        q.id && q.type && q.question && q.points && q.explanation
                    );
                    
                    if (hasValidQuestions && hasCorrectStructure) {
                        addResult('ai-results', `✅ AI question generation working! Generated ${data.questions.length} valid questions`, 'success');
                        addResult('ai-results', `Sample question: ${data.questions[0].question}`, 'info');
                    } else {
                        addResult('ai-results', `❌ AI generated invalid question structure`, 'error');
                    }
                } else {
                    addResult('ai-results', `❌ AI integration test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('ai-results', `❌ AI integration error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            addResult('api-results', 'Mathematics Assessment Test Suite Ready', 'info');
            addResult('db-results', 'Database Schema Test Ready', 'info');
            addResult('frontend-results', 'Frontend Integration Test Ready', 'info');
            addResult('ai-results', 'AI Integration Test Ready', 'info');
        });
    </script>
</body>
</html>
