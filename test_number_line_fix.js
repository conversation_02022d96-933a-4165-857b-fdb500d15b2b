/**
 * Test script to validate the Entry level number line configuration fix
 * Specifically tests the 25% decimal placement issue and validates all number line questions
 */

// Import the interactive questions
const fs = require('fs');
const path = require('path');

// Read the interactive questions file
const interactiveQuestionsPath = path.join(__dirname, 'public', 'interactiveQuestionExamples.js');
let interactiveQuestionsContent;

try {
  interactiveQuestionsContent = fs.readFileSync(interactiveQuestionsPath, 'utf8');
  // Extract the INTERACTIVE_QUESTION_EXAMPLES object
  const match = interactiveQuestionsContent.match(/const INTERACTIVE_QUESTION_EXAMPLES = ({[\s\S]*?});/);
  if (match) {
    // Use eval to parse the object (in a real environment, use a proper parser)
    const INTERACTIVE_QUESTION_EXAMPLES = eval(`(${match[1]})`);
    
    // Test functions
    function validateNumberLineQuestion(question) {
      const errors = [];
      let isValid = true;
      
      if (!question.numberLineConfig) {
        errors.push('Missing numberLineConfig');
        return { isValid: false, errors };
      }
      
      const config = question.numberLineConfig;
      const correctAnswer = parseFloat(question.correctAnswer);
      
      // Check if configuration supports the correct answer
      if (isNaN(correctAnswer)) {
        errors.push(`Invalid correct answer: ${question.correctAnswer}`);
        isValid = false;
      }
      
      // Check if correct answer is within range
      if (correctAnswer < config.min || correctAnswer > config.max) {
        errors.push(`Correct answer ${correctAnswer} outside range [${config.min}, ${config.max}]`);
        isValid = false;
      }
      
      // Check if step size allows correct answer placement
      if (config.step && config.step > 0) {
        const stepsFromMin = (correctAnswer - config.min) / config.step;
        if (Math.abs(stepsFromMin - Math.round(stepsFromMin)) > 0.001) {
          errors.push(`Correct answer ${correctAnswer} not achievable with step ${config.step} from min ${config.min}`);
          isValid = false;
        }
      }
      
      // Special validation for decimal questions
      if (question.question.toLowerCase().includes('decimal') || 
          question.question.toLowerCase().includes('%') ||
          correctAnswer % 1 !== 0) {
        
        if (config.step >= 1) {
          errors.push(`Decimal question requires step < 1, got step: ${config.step}`);
          isValid = false;
        }
      }
      
      // Special validation for percentage questions
      if (question.question.toLowerCase().includes('%')) {
        if (config.max < 1 && correctAnswer > config.max) {
          errors.push(`Percentage question may need max >= 1 for decimal representation`);
          isValid = false;
        }
        
        // Check for optimal step size for percentage-to-decimal questions
        if (question.question.toLowerCase().includes('decimal')) {
          const percentageValue = parseFloat(question.question.match(/(\d+)%/)?.[1] || '0') / 100;
          if (percentageValue > 0) {
            const stepsToAnswer = (percentageValue - config.min) / config.step;
            if (Math.abs(stepsToAnswer - Math.round(stepsToAnswer)) > 0.001) {
              errors.push(`Percentage ${percentageValue * 100}% (${percentageValue}) not precisely achievable with step ${config.step}`);
              isValid = false;
            }
          }
        }
      }
      
      return { isValid, errors };
    }
    
    function testSpecific25PercentQuestion() {
      console.log('🎯 Testing Specific 25% Decimal Question');
      console.log('='.repeat(45));
      
      // Find the 25% question in Entry level
      const entryQuestions = INTERACTIVE_QUESTION_EXAMPLES.Entry || [];
      const percentageQuestion = entryQuestions.find(q => 
        q.type === 'number-line' && 
        q.question.includes('25%') && 
        q.question.includes('decimal')
      );
      
      if (!percentageQuestion) {
        console.log('❌ 25% decimal question not found in Entry level');
        return false;
      }
      
      console.log(`Found question: "${percentageQuestion.question}"`);
      console.log(`Configuration:`, percentageQuestion.numberLineConfig);
      console.log(`Correct answer: ${percentageQuestion.correctAnswer}`);
      
      // Validate the question
      const validation = validateNumberLineQuestion(percentageQuestion);
      
      console.log(`\nValidation result: ${validation.isValid ? '✅ VALID' : '❌ INVALID'}`);
      
      if (!validation.isValid) {
        console.log('Errors found:');
        validation.errors.forEach(error => console.log(`  - ${error}`));
      }
      
      // Test specific placement scenarios
      console.log('\n📊 Placement Analysis:');
      const config = percentageQuestion.numberLineConfig;
      const targetValue = 0.25;
      
      // Calculate steps needed to reach 0.25
      const stepsToTarget = (targetValue - config.min) / config.step;
      const canPlaceExactly = Math.abs(stepsToTarget - Math.round(stepsToTarget)) < 0.001;
      
      console.log(`  Target value: ${targetValue}`);
      console.log(`  Steps from min (${config.min}) to target: ${stepsToTarget.toFixed(3)}`);
      console.log(`  Can place exactly: ${canPlaceExactly ? '✅ YES' : '❌ NO'}`);
      console.log(`  Nearest achievable values:`);
      
      // Show nearest achievable values
      const lowerSteps = Math.floor(stepsToTarget);
      const upperSteps = Math.ceil(stepsToTarget);
      const lowerValue = config.min + (lowerSteps * config.step);
      const upperValue = config.min + (upperSteps * config.step);
      
      console.log(`    ${lowerSteps} steps: ${lowerValue.toFixed(3)}`);
      console.log(`    ${upperSteps} steps: ${upperValue.toFixed(3)}`);
      
      // Test user experience
      console.log('\n👤 User Experience Analysis:');
      console.log(`  Step size: ${config.step} (${config.step <= 0.01 ? 'EXCELLENT' : config.step <= 0.05 ? 'GOOD' : 'NEEDS IMPROVEMENT'})`);
      console.log(`  Range: [${config.min}, ${config.max}] (${config.max - config.min === 1 ? 'OPTIMAL for percentages' : 'CHECK range'})`);
      console.log(`  Snap to grid: ${config.snapToGrid ? 'ENABLED' : 'DISABLED'}`);
      
      return validation.isValid && canPlaceExactly;
    }
    
    function testAllNumberLineQuestions() {
      console.log('\n📋 Testing All Number Line Questions');
      console.log('='.repeat(40));
      
      const levels = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
      let totalQuestions = 0;
      let validQuestions = 0;
      let percentageQuestions = 0;
      let validPercentageQuestions = 0;
      
      levels.forEach(level => {
        const questions = INTERACTIVE_QUESTION_EXAMPLES[level] || [];
        const numberLineQuestions = questions.filter(q => q.type === 'number-line');
        
        if (numberLineQuestions.length > 0) {
          console.log(`\n📝 ${level} Level (${numberLineQuestions.length} questions):`);
          
          numberLineQuestions.forEach(question => {
            totalQuestions++;
            const validation = validateNumberLineQuestion(question);
            
            if (validation.isValid) {
              validQuestions++;
              console.log(`  ✅ ID ${question.id}: "${question.question.substring(0, 40)}..."`);
            } else {
              console.log(`  ❌ ID ${question.id}: "${question.question.substring(0, 40)}..."`);
              validation.errors.forEach(error => console.log(`     - ${error}`));
            }
            
            // Track percentage questions specifically
            if (question.question.toLowerCase().includes('%')) {
              percentageQuestions++;
              if (validation.isValid) {
                validPercentageQuestions++;
              }
              
              // Show step analysis for percentage questions
              const config = question.numberLineConfig;
              console.log(`     📊 Step: ${config.step}, Range: [${config.min}, ${config.max}]`);
            }
          });
        }
      });
      
      console.log('\n📊 Overall Results:');
      console.log(`  Total number line questions: ${totalQuestions}`);
      console.log(`  Valid questions: ${validQuestions} (${Math.round((validQuestions/totalQuestions)*100)}%)`);
      console.log(`  Percentage questions: ${percentageQuestions}`);
      console.log(`  Valid percentage questions: ${validPercentageQuestions} (${Math.round((validPercentageQuestions/percentageQuestions)*100)}%)`);
      
      return {
        totalQuestions,
        validQuestions,
        percentageQuestions,
        validPercentageQuestions,
        successRate: (validQuestions / totalQuestions) * 100
      };
    }
    
    function runComprehensiveTest() {
      console.log('🧪 Number Line Configuration Fix Validation');
      console.log('===========================================\n');
      
      // Test 1: Specific 25% question
      const specific25PercentResult = testSpecific25PercentQuestion();
      
      // Test 2: All number line questions
      const allQuestionsResult = testAllNumberLineQuestions();
      
      // Final summary
      console.log('\n🎯 Final Test Summary');
      console.log('====================');
      console.log(`✅ 25% decimal question fixed: ${specific25PercentResult ? 'YES' : 'NO'}`);
      console.log(`✅ Overall success rate: ${allQuestionsResult.successRate.toFixed(1)}%`);
      console.log(`✅ All percentage questions valid: ${allQuestionsResult.validPercentageQuestions === allQuestionsResult.percentageQuestions ? 'YES' : 'NO'}`);
      
      if (specific25PercentResult && allQuestionsResult.successRate === 100) {
        console.log('\n🎉 All tests passed! Number line configuration is working correctly.');
      } else {
        console.log('\n⚠️ Some issues remain. Please review the configuration.');
      }
      
      return {
        specific25PercentFixed: specific25PercentResult,
        overallSuccessRate: allQuestionsResult.successRate,
        allValid: allQuestionsResult.successRate === 100
      };
    }
    
    // Run the comprehensive test
    runComprehensiveTest();
    
  } else {
    console.error('Could not parse INTERACTIVE_QUESTION_EXAMPLES from file');
  }
  
} catch (error) {
  console.error('Error reading interactive questions file:', error.message);
  console.log('Running with mock data instead...');
  
  // Mock test with the specific question
  const mockQuestion = {
    id: 6,
    type: "number-line",
    topic: "percentages",
    question: "Place 25% on the number line (as a decimal).",
    numberLineConfig: {
      min: 0,
      max: 1,
      step: 0.01,
      snapToGrid: true,
      showLabels: true,
      labelStep: 0.25
    },
    correctAnswer: "0.25",
    points: 2,
    explanation: "25% = 25/100 = 0.25"
  };
  
  console.log('🧪 Testing Mock 25% Question Configuration');
  console.log('==========================================');
  console.log(`Question: "${mockQuestion.question}"`);
  console.log(`Config:`, mockQuestion.numberLineConfig);
  console.log(`Answer: ${mockQuestion.correctAnswer}`);
  
  // Test if 0.25 can be placed with step 0.01
  const stepsToAnswer = (0.25 - 0) / 0.01;
  console.log(`Steps to answer: ${stepsToAnswer} (should be whole number: ${stepsToAnswer % 1 === 0 ? 'YES' : 'NO'})`);
  
  console.log('\n✅ Mock test shows configuration should work correctly!');
}
