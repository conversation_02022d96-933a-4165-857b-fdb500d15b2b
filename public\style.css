@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap");

body {
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    background: linear-gradient(rgba(20, 71, 187, 0.3), rgba(255, 255, 255, 0.2)), url("BG.png") no-repeat center center fixed;
    background-size: cover;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
}

/* Global utility classes */
.hidden {
    display: none !important;
}

#header {
    display: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 15px;
}

.header-texts {
    display: flex;
    justify-content: space-around;
    width: 100%;
    align-items: center;
    font-family: 'Montserrat', sans-serif;
    font-size: 15px;
}

@media (max-width: 768px) {
    #header {
        padding: 10px;
        font-size: 12px;
        flex-direction: column;
        height: auto;
        justify-content: center;
    }

    .header-texts, #header h2 {
        flex-direction: column;
        text-align: center;
    }

    #header h2 {
        margin: 5px 0;
        font-size: 12px;
    }
}

#failure-container h2 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.4;
}

#failure-container h2:first-of-type {
    font-size: 18px;
    font-weight: 600;
    color: #1547BB;
}

#failure-container .blue-text {
    color: #1547BB;
}

#score-container {
    width: 90%;
    max-width: 550px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    font-weight: 400;
    font-size: 15px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    max-height: 80vh;
    overflow-y: auto;
    font-family: 'Montserrat', sans-serif;
}

@media (max-width: 768px) {
    #quiz-container,
    #score-container {
        padding: 20px;
        font-size: 12px;
    }
}

.option-btn,
#highscore-btn,
#next-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 10px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

.option-btn:hover,
#highscore-btn:hover,
#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#success-container h2 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#success-container .centered-image {
    max-width: 100px;
    margin-bottom: 30px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    transition: transform 0.3s ease;
}

#success-container .centered-image:hover {
    transform: scale(1.1);
}

#final-success-container {
    width: 90%;
    max-width: 600px;
    background: #ffffff60;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    margin-top: 80px;
    font-weight: 400;
    font-size: 15px;
    position: relative;
    backdrop-filter: blur(16px);
    z-index: 100;
    font-family: "Montserrat", sans-serif;
}

@media (max-width: 768px) {
    #final-success-container {
        padding: 20px;
        font-size: 12px;
    }
}

#final-success-container h2 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    text-transform: uppercase;
    line-height: 1.4;
}

#final-success-container .centered-image {
    max-width: 200px;
    margin-bottom: 30px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    transition: transform 0.3s ease;
}

#final-success-container .centered-image:hover {
    transform: scale(1.1);
}

#result-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 30px;
}

#start-page {
    width: 90%;
    max-width: 500px;
    padding: 24px;
    border-radius: 12px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: fadeIn 1.2s ease-out forwards;
}

@media (max-width: 768px) {
    #start-page {
        padding: 20px;
    }
}

#start-page h1 {
    font-size: 22px;
    font-weight: 400;
    color: #1447BA;
    text-align: center;
    margin-bottom: 10px;
    line-height: 1.3;
}

#start-page h2 {
    font-size: 22px;
    font-weight: 700;
    color: #1447BA;
    text-align: center;
    margin-top: 10px;
    line-height: 1.3;
}

#start-page p {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    margin-bottom: 34px;
    line-height: 1.5;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.6s;
}

#start-page img {
    width: 200px;
    height: auto;
    margin: 1rem auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.3s;
}

#start-btn {
    background-color: #1547bb;
    color: #fffffff6;
    padding: 14px 28px;
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: contentFadeIn 1.2s ease-out forwards;
    animation-delay: 0.9s;
}

@media (max-width: 768px) {
    #start-btn {
        padding: 12px 24px;
        font-size: 13px;
    }
}

#start-btn:hover {
    background-color: #121c41;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

#progress-bar {
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#progress-bar-fill {
    display: block;
    height: 100%;
    width: 0;
    background: #1547bb;
    transition: width 0.5s ease-in-out;
}

.skip-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 10px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.skip-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.option-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

.option-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.correct {
    background: #28a745 !important; /* Changed from #7ae582 to a more visible green */
}

.wrong {
    background: #e74c3c !important;
}

#next-btn {
    background-color: #7ae582;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
}

#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#success-container,
#failure-container,
#confirmation-container,
#result-container {
    width: 90%;
    max-width: 800px;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 40px auto;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    display: none;
    font-size: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    #success-container,
    #failure-container,
    #confirmation-container,
    #result-container {
        width: 95%;
        padding: 15px;
        margin: 20px auto;
        font-size: 13px;
        max-height: 90vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

#success-container h2#success-heading,
#result-container h2#result-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#success-container h2#success-heading1,
#result-container h3#remarks {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    color: #1547BB;
}

#failure-container h2#recommendation-text,
#result-container p {
    font-size: 15px;
    font-weight: 400;
    color: #1447BA;
    margin-bottom: 20px;
    line-height: 1.6;
}

@media (max-width: 768px) {
    #success-container h2#success-heading,
    #result-container h2#result-text {
        font-size: 15px;
    }

    #success-container h2#success-heading1,
    #result-container h3#remarks {
        font-size: 13px;
    }

    #failure-container h2#recommendation-text,
    #result-container p {
        font-size: 13px;
        line-height: 1.5;
    }
}

#result-container .book-btn,
.book-btn {
    font-size: 15px;
    font-weight: bold;
    padding: 12px 24px;
    margin-top: 20px;
    background-color: #1547bb;
    color: #ffffff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
}

@media (max-width: 768px) {
    #result-container .book-btn,
    .book-btn {
        font-size: 13px;
        padding: 10px 20px;
    }
}

#result-container .book-btn:hover,
.book-btn:hover {
    background-color: #121c41;
}

#result-container .book-text,
.book-text {
    font-size: 15px;
    font-weight: 700;
    color: #1447BA;
    margin-top: 15px;
    display: block;
}

@media (max-width: 768px) {
    #result-container .book-text,
    .book-text {
        font-size: 13px;
    }
}

#result-container .book-text .regular-text,
.book-text .regular-text {
    font-size: 15px;
    font-weight: 400;
    text-decoration: none;
    color: #1447BA;
    display: block;
    margin-top: 10px;
}

@media (max-width: 768px) {
    #result-container .book-text .regular-text,
    .book-text .regular-text {
        font-size: 13px;
    }
}

#result-container .book-text a,
.book-text a,
.book-link {
    text-decoration: underline;
    color: inherit;
    transition: color 0.3s ease;
}

#result-container .book-text a:hover,
.book-text a:hover,
.book-link:hover {
    color: #1547bb;
}

#confirmation-container {
    width: 80%;
    max-width: 650px;
}

@media (max-width: 768px) {
    #confirmation-container {
        width: 95%;
        padding: 15px;
    }
}

#confirmation-container h2 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#confirmation-container h3 {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #1547BB;
}

@media (max-width: 768px) {
    #confirmation-container h2 {
        font-size: 15px;
    }

    #confirmation-container h3 {
        font-size: 13px;
    }
}

#logo-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

#logo-container img {
    width: 200px;
    height: auto;
    max-width: 80%;
    max-height: 80%;
}

@media (max-width: 768px) {
    #logo-container {
        bottom: 10px;
        right: 10px;
    }

    #logo-container img {
        width: 120px;
        max-width: 50%;
        max-height: 50%;
    }
}


#pathway-container {
    display: none;
    max-width: 800px;
    width: 90%;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 40px auto;
    font-family: 'Montserrat', sans-serif;
    color: #1547BB;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
}

#pathway-container .close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

#pathway-container .close-btn:hover {
    color: #1547BB;
}

#result-text {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #1547BB;
}

.pathway-info {
    font-size: 15px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 20px;
    line-height: 1.6;
}

.confidence-text,
.discuss-text {
    font-size: 15px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 20px;
    line-height: 1.6;
}

#suggested-pathway {
    font-weight: bold;
}

@media (max-width: 768px) {
    #pathway-container {
        width: 95%;
        padding: 15px;
        margin: 20px auto;
        font-size: 13px;
    }

    .confidence-text,
    .discuss-text {
        font-size: 13px;
    }
}

ul.course-list {
    margin-top: 20px;
}




.course-list {
    list-style-type: none;
    font-size: 15px;
    color: #1547BB;
    padding: 0 10px;
    margin: 0 auto 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.course-list li {
    color: #1547BB;
    font-size: 13px;
    font-weight: 400;
    margin: 5px;
    padding: 0 0 0 15px;
    position: relative;
}

.course-list li::before {
    content: "\2022";  /* Unicode for bullet point */
    position: absolute;
    left: 0;
    color: #1547BB;
}

@media (max-width: 768px) {
    .course-list {
        font-size: 13px;
        padding: 0 5px;
    }

    .course-list li {
        font-size: 11px;
        margin: 3px;
        padding: 0 0 0 12px;
    }
}

/* Enhanced Course Recommendations Styles */
.course-recommendations {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.course-recommendations h4 {
    color: #1e40af;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3b82f6;
    padding-bottom: 0.5rem;
}

.recommendation-description {
    background: #eff6ff;
    border-left: 4px solid #3b82f6;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 4px 4px 0;
}

.recommendation-description p {
    margin: 0;
    color: #1e40af;
    font-weight: 500;
}

.eligible-courses h5,
.next-steps-guidance h5 {
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
    margin: 1rem 0 0.5rem 0;
}

.course-item {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
    color: #1f2937;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.course-item:hover {
    background: #f3f4f6;
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.next-steps-guidance p {
    color: #4b5563;
    line-height: 1.6;
    margin: 0.5rem 0;
}

.contact-info {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.contact-info h4 {
    color: #92400e;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contact-info p {
    color: #78350f;
    margin: 0;
    line-height: 1.5;
}

/* Natural Response Question Styles */
.natural-response-note {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    color: #0369a1;
    font-size: 0.875rem;
    font-style: italic;
    line-height: 1.4;
}

.grammar-textarea,
.sentence-textarea {
    width: 100%;
    min-height: 80px;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.grammar-textarea:focus,
.sentence-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.word-bank-display {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1rem;
    margin: 0.75rem 0;
    font-size: 1rem;
}

.word-bank-display strong {
    color: #374151;
    font-weight: 600;
}

.original-sentence {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 1rem;
    margin: 0.75rem 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: #92400e;
    font-style: italic;
}
/* Scrollbar styles - only apply on smaller devices */
@media (max-width: 768px) {
    #success-container,
    #failure-container,
    #confirmation-container,
    #result-container,
    #pathway-container {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;
    }

    #success-container::-webkit-scrollbar,
    #failure-container::-webkit-scrollbar,
    #confirmation-container::-webkit-scrollbar,
    #result-container::-webkit-scrollbar,
    #pathway-container::-webkit-scrollbar {
        width: 6px;
    }

    #success-container::-webkit-scrollbar-track,
    #failure-container::-webkit-scrollbar-track,
    #confirmation-container::-webkit-scrollbar-track,
    #result-container::-webkit-scrollbar-track,
    #pathway-container::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    #success-container::-webkit-scrollbar-thumb,
    #failure-container::-webkit-scrollbar-thumb,
    #confirmation-container::-webkit-scrollbar-thumb,
    #result-container::-webkit-scrollbar-thumb,
    #pathway-container::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 3px;
    }
}

/* Container styles */
.form-container {
    max-width: 500px;
    margin: 2rem auto;
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  /* Heading styles */
  .form-container h2 {
    font-size: 1.25rem;
    color: #1a365d;  /* Darker blue for better contrast */
    margin-bottom: 1.5rem;
    text-align: left;
    font-weight: 500;
  }

  /* Form row styles */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  @media (max-width: 600px) {
    .form-row {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  /* Form group styles */
  .form-group {
    flex: 1;
    position: relative;
    margin-bottom: 1.5rem;
  }

  /* Input styles */
  .form-group input {
    width: 100%;
    padding: 0.75rem 2rem 0.5rem 0;
    font-size: 0.875rem;
    color: #2d3748;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    background: transparent;
    transition: border-color 0.2s ease;
  }

  .form-group input:focus {
    outline: none;
    border-bottom-color: #3182ce;  /* Professional blue */
  }

  /* Label styles */
  .form-group label {
    position: absolute;
    top: 0.75rem;
    left: 0;
    font-size: 0.875rem;
    color: #718096;  /* Subtle gray */
    pointer-events: none;
    transition: all 0.2s ease;
  }

  /* Floating label effect */
  .form-group input:focus ~ label,
  .form-group input:not(:placeholder-shown) ~ label {
    top: -0.5rem;
    font-size: 0.75rem;
    color: #3182ce;  /* Professional blue */
  }

  /* Validation icon styles */
  #email-validation-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    pointer-events: none;
  }

  /* Submit button styles */
  button#submit-form {
    margin-top: 1.5rem;
    width: 100%;
    padding: 0.75rem;
    background-color: #1547bb;  /* Updated to main blue */
    color: #ffffff;
    font-size: 0.875rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  button#submit-form:hover {
    background-color: #121c41;  /* Updated to dark blue on hover */
  }

  /* Success state */
  .form-group.success input {
    border-bottom-color: #7ae582;  /* Updated to official green */
  }

  .form-group.success label {
    color: #7ae582;  /* Updated to official green */
  }

  /* Error state */
  .form-group.error input {
    border-bottom-color: #e53e3e;  /* Error red */
  }

  .form-group.error label {
    color: #e53e3e;
  }

  /* Optional: Add subtle hover effect on inputs */
  .form-group input:hover {
    border-bottom-color: #cbd5e0;
  }

  .role-suggestions {
    margin-top: 0.5rem;
    position: relative;
    z-index: 10;
  }

  .role-suggestions-scroll {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 5.5rem; /* Approximately 2-3 rows */
    overflow-y: auto;
    padding-right: 0.5rem;
    scrollbar-width: thin;
  }

  /* Custom scrollbar styling */
  .role-suggestions-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
  }

  .role-suggestions-scroll::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }

  .suggestion-button {
    font-size: 0.7rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    background-color: #f9fafb;
    color: #4b5563;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    white-space: nowrap;
    height: 1.75rem;
  }

  .suggestion-button:hover {
    background-color: #eff6ff;
    color: #1547bb;
    border-color: #bfdbfe;
  }

  .suggestion-button:focus {
    outline: none;
    ring: 2px;
    ring-color: #1547bb;
    ring-opacity: 0.5;
  }

#terms-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

#terms-checkbox {
    margin-right: 10px;
}

#terms-container a {
    color: #1547BB;
    text-decoration: none;
}

#terms-container a:hover {
    text-decoration: underline;
}

#loading-overlay {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
}

.loading-spinner::before,
.loading-spinner::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    animation: pulse 1.8s ease-in-out infinite;
}

.loading-spinner::before {
    width: 100%;
    height: 100%;
    background-color: rgba(52, 152, 219, 0.5);
    animation-delay: -0.9s;
}

.loading-spinner::after {
    width: 75%;
    height: 75%;
    background-color: rgba(52, 152, 219, 0.7);
    top: 12.5%;
    left: 12.5%;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.5;
    }
}

.loading-text {
    color: #fff;
    font-size: 0.75rem; /* 12px */
    letter-spacing: 0.5px;
    opacity: 0;
    animation: fadeInOut 2s ease-in-out infinite;
    text-align: center;
}

.lottie-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px; /* Reduced from 60px to 50px */
    height: 50px; /* Reduced from 60px to 50px */
}

.loading-overlay-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem; /* Reduced gap from 1rem to 0.75rem for better proportion */
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

#key-input-container {
    position: relative;
    margin-bottom: 30px;
}

#key-input-container input {
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 20px;
    background-color: transparent;
    font-size: 14px;
    transition: border-color 0.3s ease;
    width: 50%;
}

@media (max-width: 480px) {
    #key-input-container input::placeholder {
        font-size: 7.5px;
    }
}

#key-input-container input:focus {
    outline: none;
    border-color: #3498db;
}

#key-input-container label {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 13px;
    color: #999;
    transition: all 0.3s ease;
    pointer-events: none;
}

#key-input-container input:focus + label,
#key-input-container input:not(:placeholder-shown) + label {
    top: -18px;
    left: 10px;
    font-size: 12px;
    color: #3498db;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 0 5px;
}

#key-error {
    color: red;
    font-size: 14px;
    margin-top: 5px;
    text-align: center;
}

@keyframes shake {
    0% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-10px);
    }

    50% {
        transform: translateX(10px);
    }

    75% {
        transform: translateX(-10px);
    }

    100% {
        transform: translateX(0);
    }
}

.shake {
    animation: shake 0.5s;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #333;
}

#consent-container {
    font-family: 'Montserrat', sans-serif;
}

#consent-container h2 {
    color: #1547BB;
    font-size: 14px;
    line-height: 1.5rem;
}

#consent-container p {
    font-size: 14px;
    line-height: 1.125rem;
    color: #333;
}

@media (min-width: 768px) {
    #consent-container p {
        font-size: 14px;
        line-height: 1.25rem;
    }
}

#consent-container p:nth-of-type(2) {
    color: #1547BB;
    font-weight: 500;
}

#consent-container label {
    font-size: 14px;
    color: #555;
}

@media (min-width: 768px) {
    #consent-container label {
        font-size: 14px;
    }
}

#consent-container a {
    color: #1547BB;
    text-decoration: none;
}

#consent-container a:hover {
    text-decoration: underline;
}

#consent-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
}

@media (min-width: 768px) {
    #consent-btn {
        font-size: 14px;
        width: auto;
    }
}

#consent-btn:hover {
    background-color: #121c41;
}

#close-consent-btn {
    transition: color 0.3s ease;
}

#close-consent-btn:hover {
    color: #333;
}

#consent-container .bg-white {
    max-height: 90vh;
    overflow-y: auto;
}

@media (max-width: 640px) {
    #consent-container .bg-white {
        padding: 10px;
    }
}
#results-summary-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    padding: 20px;
    z-index: 9999;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
}

@media (max-width: 768px) {
    #results-summary-container {
        padding: 20px;
    }
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.summary-header h2 {
    font-size: 16px;
    font-weight: 700;
    color: #1547BB;
    margin: 0;
}

.close-btn {
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #888;
}

.score-summary {
    text-align: center;
    margin-bottom: 30px;
}

.score-summary h4 {
    font-size: 14px;
    font-weight: 400;
    color: #1547BB;
    margin-bottom: 10px;
}

.score-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f2f2f2;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 20px;
    background-color: #f2f2f2;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background-color: #1547bb;
    transition: width 0.5s ease;
}

.remarks {
    font-size: 14px;
    color: #1547BB;
    margin-bottom: 30px;
    line-height: 1.6;
}

.section-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

#results-summary-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--background-light);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    padding: 30px;
    z-index: 9999;
    font-family: 'Montserrat', sans-serif;
    color: var(--text-dark);
    display: flex;
    flex-direction: column;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--glass-border);
}

.summary-header h2 {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-blue);
    margin: 0;
}

.close-btn {
    font-size: 24px;
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--text-dark);
}

.summary-content {
    flex-grow: 1;
    overflow-y: auto;
    padding-right: 15px;
}

.section-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.section {
    background-color: var(--glass-background);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 6px var(--glass-shadow);
}

.section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--glass-shadow);
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 15px;
}

.score-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

.user-score {
    color: var(--secondary-blue);
}

.total-questions {
    margin: 0 5px;
    color: var(--text-light);
}

.max-score {
    color: var(--text-light);
}

.progress-bar {
    height: 8px;
    background-color: var(--glass-background);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background-color: var(--secondary-blue);
    transition: width 0.5s ease;
    background-color: #1547bb;
}

.remarks {
    font-size: 14px;
    text-align: center;
    line-height: 1.5;
    color: var(--text-light);
}

.summary-content::-webkit-scrollbar {
    width: 8px;
}

.summary-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.summary-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.summary-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.summary-content {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

@media (max-width: 768px) {
    #results-summary-container {
        padding: 20px;
    }

    .summary-header h2 {
        font-size: 14px;
    }

    .section h3 {
        font-size: 14px;
    }

    .score-container {
        font-size: 14px;
    }
}

#quiz-container {
    width: 90%;
    max-width: 800px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: box-shadow 0.3s ease;
    font-weight: 400;
    font-size: 14px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

@media (max-width: 768px) {
    #quiz-container {
        padding: 15px;
        width: 95%;
    }

    #options-container {
        grid-template-columns: 1fr !important;
    }

    .option-btn {
        font-size: 13px;
        padding: 10px 12px;
        white-space: normal;
        height: auto;
        min-height: 44px;
    }
}

#question {
    font-size: 16px;
    color: #333;
    font-weight: 450;
    margin-top: 10px;
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
}

.option-btn {
    background-color: #1547bb;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.option-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#next-btn {
    background-color: #7ae582;
    color: #fff;
    padding: 10px 18px;
    margin: 5px 0;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

#next-btn:hover {
    background: #1547bb;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

#progress-bar {
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#progress-bar-fill {
    display: block;
    height: 100%;
    width: 0;
    background: #1547bb;
    transition: width 0.5s ease-in-out;
}



.action-button {
    display: inline-block;
    padding: 12px 24px;
    margin: 10px;
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    outline: none;
}



#next-section-btn {
    background-color: #3498db;
    color: #ffffff;
    border: 2px solid #3498db;
}

#next-section-btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#restart-btn {
    background-color: #ffffff;
    color: #3498db;
    border: 2px solid #3498db;
}

#restart-btn:hover {
    background-color: #3498db;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .action-button {
        display: block;
        width: 80%;
        margin: 10px auto;
    }
}

#result-container {
    width: 95%;
    max-width: 800px;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    margin: 20px auto;
    font-family: 'Montserrat', sans-serif;
    color: #333333;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: 90vh;
}

@media (max-width: 768px) {
    #result-container {
        padding: 15px;
        font-size: 12px;
    }
}

#result-container h2#result-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 1.4;
    text-transform: uppercase;
    color: #1547BB;
}

#result-container h3#remarks {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    color: #1547BB;
}

#result-container p {
    font-size: 14px;
    font-weight: 400;
    color: #1447BA;
    margin-bottom: 20px;
    line-height: 1.6;
}

@media (max-width: 768px) {
    #result-container h2#result-text {
        font-size: 14px;
    }

    #result-container h3#remarks {
        font-size: 13px;
    }

    #result-container p {
        font-size: 12px;
        line-height: 1.5;
    }
}

#result-container .book-btn,
.book-btn {
    font-size: 14px;
    font-weight: bold;
    padding: 12px 24px;
    margin-top: 20px;
    background-color: #1547bb;
    color: #ffffff;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
}

@media (max-width: 768px) {
    #result-container .book-btn,
    .book-btn {
        font-size: 12px;
        padding: 10px 20px;
    }
}

#result-container .book-btn:hover,
.book-btn:hover {
    background-color: #121c41;
}

#result-container .book-text,
.book-text {
    font-size: 14px;
    font-weight: 700;
    color: #1447BA;
    margin-top: 15px;
    display: block;
}

@media (max-width: 768px) {
    #result-container .book-text,
    .book-text {
        font-size: 12px;
    }
}

#result-container .book-text .regular-text,
.book-text .regular-text {
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;
    color: #1447BA;
    display: block;
    margin-top: 10px;
}

@media (max-width: 768px) {
    #result-container .book-text .regular-text,
    .book-text .regular-text {
        font-size: 12px;
    }
}

#result-container .book-text a,
.book-text a,
.book-link {
    text-decoration: underline;
    color: inherit;
    transition: color 0.3s ease;
}

#result-container .book-text a:hover,
.book-text a:hover,
.book-link:hover {
    color: #1547bb;
}

/* Scrollbar styles for the result container */
#result-container::-webkit-scrollbar {
    width: 6px;
}

#result-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#result-container::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
}

#result-container::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

#result-container {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}



.notification {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 50;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 24rem;
    transition: opacity 300ms ease-in-out;
    opacity: 0;
}

.notification.success {
    background-color: #f0fff4;
    border: 1px solid #68d391;
    color: #2f855a;
}

.notification.error {
    background-color: #fff5f5;
    border: 1px solid #fc8181;
    color: #c53030;
}

.feedback-container, .feedback-form {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 24px;
    max-width: 600px;
    margin: 20px auto;
    font-family: 'Montserrat', sans-serif;
  }

  .feedback-container h3, .feedback-form h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1547BB;
    margin-bottom: 20px;
    text-align: center;
  }

  .feedback-btn, .submit-feedback {
    background-color: #1547BB;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: inline-block;
    margin: 0 10px;
  }

  .feedback-btn:hover, .submit-feedback:hover {
    background-color: #121c41;
  }

  .feedback-question {
    margin-bottom: 24px;
  }

  .feedback-question p {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
  }

  .rating-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .rating-container label {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    margin: 0 5px;
  }

  .rating-container label:hover {
    transform: translateY(-2px);
  }

  .rating-container input[type="radio"] {
    display: none;
  }

  .rating-container span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    min-width: 80px;
    height: 40px;
    border-radius: 20px;
    border: 2px solid #1547BB;
    font-size: 12px;
    font-weight: 600;
    color: #1547BB;
    padding: 0 10px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    text-align: center;
  }

  .rating-container input[type="radio"]:checked + span {
    background-color: #1547BB;
    color: #ffffff;
  }

  @media (max-width: 768px) {
    .feedback-container, .feedback-form {
      padding: 20px;
    }

    .rating-container {
      flex-direction: column;
      align-items: stretch;
    }

    .rating-container label {
      margin-bottom: 10px;
    }

    .rating-container span {
      width: 100%;
      height: 40px;
      font-size: 12px;
    }
  }

  #pathway-btn {
    display: none;
    background-color: #1547bb;
    color: #ffffff;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

#pathway-btn:hover {
    background-color: #121c41;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    #pathway-btn {
        font-size: 13px;
        padding: 10px 20px;
    }
}

/* Ensure the button is a block-level element on smaller screens for better spacing */
@media (max-width: 480px) {
    #pathway-btn {
        display: block;
        width: 80%;
        margin: 20px auto;
    }
}

.validation-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 9999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-weight: 500;
  }

  .notification.success {
    background-color: #48bb78;
    color: white;
  }

  .notification.error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FCA5A5;
  }

  #role-validation-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    pointer-events: none;
}

.form-group {
    position: relative;
}

/* Container for role suggestions */
.role-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 150px;
    background: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: 10;
    display: none;
    padding: 0.75rem 0.5rem;
    overflow: hidden; /* Changed from overflow-y: auto and overflow-x: hidden */
}

/* New mobile-optimized class for role suggestions */
.role-suggestions-scroll {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 120px;
    overflow-y: auto;
    padding-right: 0.5rem;
    scrollbar-width: thin;
}

/* Update suggestion-tab styles for better mobile compatibility */
.suggestion-tab {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    color: #4a5568;
    background-color: #f3f4f6;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0.25rem;
    border: 1px solid #e5e7eb;
}

/* Hover effect for tabs */
.suggestion-tab:hover {
    background: #3182ce; /* Blue background on hover */
    color: #ffffff; /* White text on hover */
    transform: scale(1.05); /* Slightly enlarge tab */
}

/* Scrollbar customization */
.role-suggestions-scroll::-webkit-scrollbar {
    width: 4px;
    height: 4px; /* Added for horizontal scrolling */
}

.role-suggestions-scroll::-webkit-scrollbar-thumb {
    background-color: #cbd5e0;
    border-radius: 3px;
}

.role-suggestions-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .role-suggestions {
        padding: 0.5rem;
    }

    /* Mobile-optimized layout - stacked suggestion tabs */
    .role-suggestions-scroll {
        display: flex;
        flex-direction: column; /* Stack tabs vertically on mobile */
        align-items: stretch;
        max-height: 150px;
        overflow-y: auto;
        gap: 0.25rem;
    }

    .suggestion-tab {
        width: 100%; /* Full width on mobile */
        white-space: normal; /* Allow text wrapping */
        height: auto;
        padding: 0.4rem 0.75rem;
        margin: 0.1rem 0;
        font-size: 0.7rem;
        text-overflow: ellipsis;
        overflow: hidden;
        display: block;
    }
}

/* Loading Overlay Styles */
.quiz-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 24, 39, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .quiz-loading-content {
    text-align: center;
    color: white;
  }

  .quiz-spinner-container {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
  }

  .quiz-circular-progress {
    transform: rotate(-90deg);
  }

  .quiz-progress-background,
  .quiz-progress-bar {
    fill: none;
    stroke-width: 8;
  }

  .quiz-progress-background {
    stroke: rgba(255, 255, 255, 0.1);
  }

  .quiz-progress-bar {
    stroke: #1547bb;
    stroke-dasharray: 339.292; /* Circumference of the circle (2 * PI * r) */
    stroke-dashoffset: 339.292; /* Start at 0% progress */
    transition: stroke-dashoffset 0.3s ease;
  }

  .quiz-progress-percentage {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .quiz-loading-text {
    font-size: 1.25rem;
    margin-top: 2rem;
    color: #E5E7EB;
    opacity: 0.9;
    font-weight: 500;
  }

  .quiz-fade-in {
    opacity: 1;
  }

  #pathway-btn {
  position: relative;
  overflow: hidden;
}

.button-loading-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.5);
  transition: width 20s linear;
}

#pathway-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#pathway-btn:disabled .button-text {
  opacity: 0.7;
}

/* Modal Loading Overlay */
#modal-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(17, 24, 39, 0.95);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-loading-content {
  text-align: center;
  color: white;
}

.modal-spinner-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
}

.modal-circular-progress {
  transform: rotate(-90deg);
}

.modal-progress-background,
.modal-progress-bar {
  fill: none;
  stroke-width: 8;
}

.modal-progress-background {
  stroke: rgba(255, 255, 255, 0.1);
}

.modal-progress-bar {
  stroke: #1547bb;
  stroke-dasharray: 339.292;
  stroke-dashoffset: 339.292;
  transition: stroke-dashoffset 0.3s ease;
}

.modal-progress-percentage {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.modal-loading-text {
  font-size: 1.25rem;
  margin-top: 2rem;
  color: #E5E7EB;
  opacity: 0.9;
  font-weight: 500;
}

#pathway-btn {
  position: relative;
  min-width: 200px; /* Ensure enough space for text */
  transition: all 0.3s ease;
}

#pathway-btn .button-text {
  position: relative;
  z-index: 2;
  transition: opacity 0.3s ease;
}

#pathway-btn:disabled .button-text {
  opacity: 0.7;
}

.processing-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text {
    color: #fff;
    font-size: 0.75rem; /* 12px */
    letter-spacing: 0.5px;
    opacity: 0;
    animation: fadeInOut 2s ease-in-out infinite;
    text-align: center;
}

.lottie-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px; /* Reduced from 60px to 50px */
    height: 50px; /* Reduced from 60px to 50px */
    margin-bottom: 0.5rem;
}

/* Start page fade-in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Add fade-in for the start page elements */
@keyframes contentFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Self-assessment specific styling */
.self-assessment-indicator {
    display: inline-block;
    background-color: #e9f4ff;
    color: #1e40af;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .response-recorded {
    display: inline-block;
    background-color: #ecfdf5;
    color: #047857;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  /* Option styling for self-assessment */
  .self-assessment .option-btn {
    position: relative;
    background-color: #f3f4f6;
    color: #1f2937;
    border: 1px solid #d1d5db;
    padding-left: 2.5rem;
    text-align: left;
  }

  .self-assessment .option-btn:hover {
    background-color: #e5e7eb;
  }

  /* Level indicator circles */
  .self-assessment .option-btn::before {
    content: "";
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
  }

  /* Level colors */
  .self-assessment .option-btn[data-level="1"]::before {
    background-color: #fecaca; /* Light red for basic */
  }

  .self-assessment .option-btn[data-level="2"]::before {
    background-color: #fde68a; /* Light yellow for intermediate */
  }

  .self-assessment .option-btn[data-level="3"]::before {
    background-color: #a7f3d0; /* Light green for advanced */
  }

  /* Selected option in self-assessment */
  .option-btn.self-assessment-selected {
    background-color: #dbeafe !important;
    border: 2px solid #3b82f6 !important;
    color: #1e40af !important;
    font-weight: 500;
  }

  .option-btn.self-assessment-selected::before {
    width: 0.85rem;
    height: 0.85rem;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  /* User Form Container - General Layout */
  #user-form-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
    position: relative;
    z-index: 2;
  }

  /* Modern Form Styles */
  .modern-form-container {
    max-width: 600px;
    width: 90%;
    margin: 1rem auto;
    max-height: 90vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .form-header {
    text-align: center;
    padding: 1.5rem 2rem 1rem 2rem;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  }

  .form-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .form-header p {
    color: #64748b;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .form-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem 2rem;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }

  .form-content::-webkit-scrollbar {
    width: 6px;
  }

  .form-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .form-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .form-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .modern-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-footer {
    flex-shrink: 0;
    padding: 1rem 2rem 1.5rem 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(248, 250, 252, 0.5);
  }

  .form-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.25rem;
  }

  .input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .input-group {
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .modern-input, .modern-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.9rem;
    background-color: #ffffff;
    color: #334155;
    transition: all 0.2s ease;
    outline: none;
  }

  .modern-input:focus, .modern-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  .modern-input::placeholder {
    color: #94a3b8;
  }

  .validation-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .input-hint {
    font-size: 0.8rem;
    color: #64748b;
    margin-top: 0.25rem;
    line-height: 1.4;
  }

  /* User Type Selector Cards */
  .user-type-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .type-card {
    position: relative;
    display: block;
    padding: 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .type-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  .type-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }

  .type-card input[type="radio"]:checked + .card-content {
    color: #3b82f6;
  }

  .type-card input[type="radio"]:checked ~ .card-check {
    opacity: 1;
    transform: scale(1);
  }

  .type-card input[type="radio"]:checked ~ .card-content .card-icon {
    transform: scale(1.1);
  }

  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
    transition: color 0.2s ease;
  }

  .card-icon {
    font-size: 1.75rem;
    transition: transform 0.2s ease;
  }

  .card-title {
    font-weight: 600;
    font-size: 1rem;
    color: #1e293b;
  }

  .card-description {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.4;
  }

  .card-check {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: #3b82f6;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
  }

  .card-check::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
  }

  /* Dynamic Sections */
  .dynamic-section {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  /* Form Actions */
  .form-actions {
    display: flex;
    justify-content: center;
  }

  .modern-submit-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    min-height: 44px;
  }

  .modern-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  }

  .modern-submit-btn:active {
    transform: translateY(0);
  }

  .btn-icon {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }

  .modern-submit-btn:hover .btn-icon {
    transform: translateX(2px);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .modern-form-container {
      margin: 0.5rem auto;
      width: 95%;
      max-height: 95vh;
    }

    .form-header {
      padding: 1rem 1.5rem 0.75rem 1.5rem;
    }

    .form-content {
      padding: 1rem 1.5rem;
    }

    .form-footer {
      padding: 0.75rem 1.5rem 1rem 1.5rem;
    }

    .input-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .user-type-selector {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .form-header h2 {
      font-size: 1.25rem;
    }

    .form-header p {
      font-size: 0.85rem;
    }

    .type-card {
      padding: 1rem;
    }

    .card-content {
      gap: 0.4rem;
    }

    .card-icon {
      font-size: 1.5rem;
    }

    .card-title {
      font-size: 0.9rem;
    }

    .card-description {
      font-size: 0.8rem;
    }

    .modern-form {
      gap: 1.25rem;
    }

    .form-section {
      gap: 0.5rem;
    }
  }

  @media (max-width: 480px) {
    .modern-form-container {
      margin: 0.25rem auto;
      width: 98%;
      max-height: 98vh;
    }

    .form-header {
      padding: 0.75rem 1rem 0.5rem 1rem;
    }

    .form-content {
      padding: 0.75rem 1rem;
    }

    .form-footer {
      padding: 0.5rem 1rem 0.75rem 1rem;
    }

    .form-header h2 {
      font-size: 1.1rem;
    }

    .form-header p {
      font-size: 0.8rem;
    }

    .modern-input, .modern-select {
      padding: 0.65rem 0.75rem;
      font-size: 0.85rem;
    }

    .modern-submit-btn {
      width: 100%;
      justify-content: center;
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }

    .section-title {
      font-size: 0.9rem;
    }

    .type-card {
      padding: 0.75rem;
    }

    .card-icon {
      font-size: 1.25rem;
    }

    .card-title {
      font-size: 0.85rem;
    }

    .card-description {
      font-size: 0.75rem;
    }
  }

  /* Landscape mobile optimization */
  @media (max-height: 600px) and (orientation: landscape) {
    .modern-form-container {
      max-height: 95vh;
    }

    .form-header {
      padding: 0.75rem 2rem 0.5rem 2rem;
    }

    .form-header h2 {
      font-size: 1.25rem;
      margin-bottom: 0.25rem;
    }

    .form-header p {
      font-size: 0.8rem;
    }

    .form-content {
      padding: 1rem 2rem;
    }

    .modern-form {
      gap: 1rem;
    }

    .form-section {
      gap: 0.5rem;
    }

    .type-card {
      padding: 1rem;
    }

    .card-content {
      gap: 0.4rem;
    }
  }

  /* English Proficiency Assessment Styles */
  #english-assessment-container {
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    overflow: hidden;
  }

  .english-assessment-wrapper {
    max-width: 800px;
    width: 100%;
    height: 95vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .english-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-align: center;
    flex-shrink: 0;
  }

  .english-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }

  .english-header p {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
  }

  .timer-container {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .timer-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    font-weight: 600;
  }

  .timer-label {
    color: #64748b;
  }

  .timer-value {
    color: #dc2626;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 2px solid #fecaca;
  }

  .timer-value.warning {
    color: #ea580c;
    border-color: #fed7aa;
    animation: pulse 1s infinite;
  }

  .timer-value.critical {
    color: #dc2626;
    border-color: #fecaca;
    animation: pulse 0.5s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .english-question-container {
    padding: 1rem 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100vh - 200px);
    min-height: 400px;
    scroll-behavior: smooth;
    scroll-padding-top: 1rem;
    scroll-padding-bottom: 1rem;
  }

  .question-text {
    margin-bottom: 1rem;
    text-align: center;
    flex-shrink: 0;
  }

  .question-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .question-text p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }

  .visual-aid-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
    padding: 0 1rem;
    overflow: hidden;
  }

  .seasons-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    max-height: 350px;
    width: auto;
    display: block;
    margin: 0 auto;
  }

  .response-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .response-label {
    display: block;
    font-size: 0.95rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.5rem;
    flex-shrink: 0;
  }

  .english-textarea {
    width: 100%;
    flex: 1;
    min-height: 150px;
    max-height: 300px;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-y: auto;
  }

  .english-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .english-textarea::placeholder {
    color: #94a3b8;
    font-style: italic;
  }

  .character-count {
    text-align: right;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #64748b;
    flex-shrink: 0;
  }

  .english-actions {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    flex-shrink: 0;
  }

  .english-submit-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
  }

  .english-submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  }

  .english-submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .btn-icon {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }

  .english-submit-btn:hover:not(:disabled) .btn-icon {
    transform: translateX(2px);
  }

  /* Enhanced loading animation for English assessment submission */
  .english-submit-btn.loading-animation {
    position: relative;
    overflow: hidden;
  }

  .english-submit-btn.loading-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Smooth container transitions for student journey */
  .smooth-transition {
    transition: opacity 0.4s ease, transform 0.4s ease;
  }

  .fade-out {
    opacity: 0;
    transform: translateY(-10px);
  }

  .fade-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Enhanced loading overlay for students */
  .student-loading-overlay {
    background: linear-gradient(135deg, rgba(21, 71, 187, 0.95) 0%, rgba(59, 130, 246, 0.95) 100%);
    backdrop-filter: blur(8px);
  }

  .student-loading-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInOut 3s ease-in-out infinite;
  }

  /* English Assessment Results Styles */
  #english-success-container,
  #english-completion-container {
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
  }

  .results-wrapper {
    max-width: 650px;
    width: 100%;
    max-height: 95vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .results-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1.25rem 1.5rem;
    text-align: center;
    flex-shrink: 0;
  }

  .results-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
  }

  .score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .score-value {
    font-size: 2rem;
    font-weight: 800;
    color: #fbbf24;
  }

  .score-level {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
  }

  .results-content {
    padding: 1.25rem 1.5rem;
    overflow-y: auto;
    flex: 1;
    -webkit-overflow-scrolling: touch;
  }

  .feedback-section {
    margin-bottom: 1.25rem;
  }

  .feedback-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.25rem;
  }

  .feedback-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .feedback-item {
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid #3b82f6;
  }

  .feedback-item h4 {
    font-size: 0.8rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.4rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .feedback-item p {
    font-size: 0.85rem;
    color: #4b5563;
    line-height: 1.4;
    margin: 0;
  }

  .overall-feedback {
    background: #eff6ff;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #dbeafe;
  }

  .overall-feedback h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
  }

  .overall-feedback p {
    font-size: 0.85rem;
    color: #374151;
    line-height: 1.5;
    margin: 0;
  }

  .strengths-improvements {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.25rem;
  }

  .strengths-section,
  .improvements-section {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }

  .strengths-section h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #059669;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }

  .strengths-section h4::before {
    content: "✓";
    background: #10b981;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.65rem;
    font-weight: bold;
  }

  .improvements-section h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }

  .improvements-section h4::before {
    content: "!";
    background: #ef4444;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.65rem;
    font-weight: bold;
  }

  .strengths-section ul,
  .improvements-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .strengths-section li,
  .improvements-section li {
    font-size: 0.8rem;
    color: #4b5563;
    line-height: 1.4;
    margin-bottom: 0.4rem;
    padding-left: 0.8rem;
    position: relative;
  }

  .strengths-section li::before {
    content: "•";
    color: #10b981;
    font-weight: bold;
    position: absolute;
    left: 0;
  }

  .improvements-section li::before {
    content: "•";
    color: #ef4444;
    font-weight: bold;
    position: absolute;
    left: 0;
  }

  .next-steps {
    background: #fef3c7;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #fbbf24;
    margin-bottom: 1.25rem;
  }

  .next-steps h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.5rem;
  }

  .next-steps p {
    font-size: 0.85rem;
    color: #78350f;
    line-height: 1.5;
    margin: 0;
  }

  .results-actions {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    padding-top: 0.5rem;
  }

  .results-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 180px;
  }

  .results-btn.primary {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    color: white;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.3);
  }

  .results-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(16, 185, 129, 0.4);
  }

  .results-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    color: white;
    box-shadow: 0 3px 8px rgba(107, 114, 128, 0.3);
  }

  .results-btn.secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(107, 114, 128, 0.4);
  }

  /* Enhanced results container animations */
  .results-wrapper {
    animation: resultsSlideIn 0.6s ease-out;
  }

  @keyframes resultsSlideIn {
    0% {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .feedback-section, .strengths-improvements, .next-steps {
    animation: contentFadeInStagger 0.8s ease-out;
    animation-fill-mode: both;
  }

  .feedback-section {
    animation-delay: 0.2s;
  }

  .strengths-improvements {
    animation-delay: 0.4s;
  }

  .next-steps {
    animation-delay: 0.6s;
  }

  @keyframes contentFadeInStagger {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced feedback grid animations */
  .feedback-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .feedback-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Question Generation Loading Styles */
  .question-generation-loading {
    display: none;
    justify-content: center;
    align-items: center;
    padding: 3rem 2rem;
    margin: 2rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .loading-content {
    text-align: center;
    max-width: 400px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .loading-subtext {
    font-size: 1rem;
    color: #6b7280;
    font-style: italic;
  }

  /* Preliminary Questions Styles */
  .preliminary-questions-container {
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
  }

  /* Scrollbar styling for preliminary questions */
  .preliminary-questions-container::-webkit-scrollbar {
    width: 8px;
  }

  .preliminary-questions-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  .preliminary-questions-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  .preliminary-questions-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .preliminary-question-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .preliminary-questions-container {
      padding: 1rem;
      margin: 1rem 0;
      max-height: 85vh;
      border-radius: 8px;
    }

    .preliminary-question-wrapper {
      padding: 0 0.5rem;
    }

    .question-content h3 {
      font-size: 1.3rem;
      margin-bottom: 1rem;
    }

    .question-body {
      margin-bottom: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .preliminary-questions-container {
      padding: 0.75rem;
      margin: 0.5rem 0;
      max-height: 90vh;
    }

    .preliminary-question-wrapper {
      padding: 0 0.25rem;
    }

    .question-content h3 {
      font-size: 1.2rem;
      line-height: 1.4;
    }
  }

  .question-progress {
    margin-bottom: 2rem;
    text-align: center;
  }

  .progress-text {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.5s ease;
  }

  .question-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .question-body {
    margin-bottom: 2rem;
  }

  .question-actions {
    text-align: center;
  }

  .question-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }

  .question-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  .question-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .question-btn .btn-icon {
    transition: transform 0.3s ease;
  }

  .question-btn:hover:not(:disabled) .btn-icon {
    transform: translateX(2px);
  }

  /* Spelling Question Styles */
  .spelling-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .sentence-container {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    text-align: center;
  }

  /* Mobile responsive spelling questions */
  @media (max-width: 768px) {
    .sentence-container {
      padding: 1.25rem;
      font-size: 1.1rem;
      line-height: 1.5;
    }

    .mistake-word {
      padding: 4px 6px;
      margin: 0 3px;
      min-height: 32px;
      display: inline-flex;
      align-items: center;
      touch-action: manipulation;
    }
  }

  @media (max-width: 480px) {
    .sentence-container {
      padding: 1rem;
      font-size: 1rem;
      text-align: left;
    }

    .mistake-word {
      padding: 6px 8px;
      margin: 2px;
      min-height: 36px;
      font-size: 1rem;
    }
  }

  .word-item {
    display: inline;
    padding: 2px 4px;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .mistake-word {
    background: #fef2f2;
    border: 2px solid #fca5a5;
    cursor: pointer;
    color: #dc2626;
    font-weight: 600;
  }

  .mistake-word:hover {
    background: #fee2e2;
    border-color: #f87171;
  }

  .mistake-word.corrected {
    background: #f0fdf4;
    border-color: #86efac;
    color: #16a34a;
  }

  .mistake-word.correction-valid {
    background: #dcfce7;
    border-color: #4ade80;
  }

  .spelling-question .hint {
    font-size: 0.9rem;
    color: #6b7280;
    text-align: center;
    font-style: italic;
  }

  /* Grammar Question Styles */
  .grammar-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1rem;
    text-align: center;
  }

  .original-sentence {
    background: #fef2f2;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ef4444;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    font-style: italic;
  }

  .grammar-textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
    min-height: 80px;
  }

  /* Mobile responsive grammar textarea */
  @media (max-width: 768px) {
    .grammar-textarea {
      padding: 1.25rem;
      font-size: 1.1rem;
      min-height: 100px;
      border-width: 3px;
    }
  }

  @media (max-width: 480px) {
    .grammar-textarea {
      padding: 1rem;
      font-size: 1rem;
      min-height: 120px;
      border-radius: 6px;
    }
  }

  .grammar-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Vocabulary Question Styles */
  .vocabulary-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1rem;
    text-align: center;
  }

  .sentence-with-blank {
    background: #f0f9ff;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #0ea5e9;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 500;
  }

  .vocab-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }

  /* Mobile responsive vocabulary options */
  @media (max-width: 768px) {
    .vocab-options {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }
  }

  .vocab-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .vocab-option:hover {
    border-color: #3b82f6;
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .vocab-option.selected {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .vocab-option input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
  }

  .option-text {
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
  }

  /* Sentence Ordering Styles */
  .sentence-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  /* Drag and Drop Question Styles */
  .drag-drop-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .drop-zone {
    min-height: 80px;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .drop-zone.drag-over {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .drop-placeholder {
    color: #64748b;
    font-style: italic;
    margin: 0;
  }

  .constructed-sentence {
    font-size: 1.1rem;
    color: #1f2937;
    font-weight: 500;
    margin: 0;
  }

  .word-bank {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    padding: 1rem;
    background: #f1f5f9;
    border-radius: 8px;
  }

  .drag-word {
    background: #3b82f6;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: grab;
    user-select: none;
    transition: all 0.2s ease;
    font-weight: 500;
  }

  .drag-word:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  .drag-word:active {
    cursor: grabbing;
    transform: scale(0.95);
  }

  /* Interactive Grammar Question Styles */
  .interactive-grammar-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .sentence-interactive {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 1rem;
    line-height: 1.8;
    font-size: 1.1rem;
    text-align: center;
  }

  .interactive-word {
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
    margin: 0 2px;
  }

  .interactive-word:hover {
    background: #e0e7ff;
  }

  .interactive-word.selected {
    background: #dc2626;
    color: white;
  }

  .corrections-display {
    margin-top: 1rem;
  }

  .selected-corrections {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 0.75rem;
    color: #dc2626;
    font-weight: 500;
    margin: 0;
  }

  /* Vocabulary Matching Question Styles */
  .vocab-matching-question .instruction {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .matching-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
  }

  .words-column h4,
  .definitions-column h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    text-align: center;
  }

  .match-word,
  .match-definition {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
  }

  .match-word:hover,
  .match-definition:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .match-word.selected {
    background: #3b82f6;
    color: white;
    border-color: #2563eb;
  }

  .matches-display {
    margin-top: 1rem;
  }

  .matches-list {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 1rem;
  }

  .match-pair {
    background: white;
    border: 1px solid #e0e7ff;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e40af;
  }

  .match-pair:last-child {
    margin-bottom: 0;
  }

  /* Reading Comprehension Question Styles */
  .reading-comprehension-question .reading-passage {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .reading-passage h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .passage-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #374151;
    margin: 0;
  }

  .reading-question h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .reading-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .reading-option {
    display: flex;
    align-items: center;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .reading-option:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  .reading-option input[type="radio"] {
    margin-right: 0.75rem;
    accent-color: #3b82f6;
  }

  .reading-option .option-text {
    font-weight: 500;
    color: #374151;
  }

  /* Mobile Responsive Styles for New Question Types */
  @media (max-width: 768px) {
    .matching-container {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .drag-word {
      padding: 0.4rem 0.8rem;
      font-size: 0.9rem;
    }

    .word-bank {
      gap: 0.5rem;
      padding: 0.75rem;
    }

    .drop-zone {
      min-height: 60px;
      padding: 0.75rem;
    }

    .sentence-interactive {
      padding: 1rem;
      font-size: 1rem;
    }

    .interactive-word {
      padding: 0.2rem 0.4rem;
      margin: 0 1px;
    }

    .match-word,
    .match-definition {
      padding: 0.6rem;
      font-size: 0.9rem;
    }

    .reading-passage {
      padding: 1rem;
    }

    .passage-text {
      font-size: 0.9rem;
    }

    .reading-option {
      padding: 0.6rem 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .drag-word {
      padding: 0.3rem 0.6rem;
      font-size: 0.85rem;
    }

    .word-bank {
      gap: 0.4rem;
      padding: 0.5rem;
    }

    .drop-zone {
      min-height: 50px;
      padding: 0.5rem;
    }

    .sentence-interactive {
      padding: 0.75rem;
      font-size: 0.9rem;
    }

    .interactive-word {
      padding: 0.15rem 0.3rem;
      font-size: 0.85rem;
    }

    .match-word,
    .match-definition {
      padding: 0.5rem;
      font-size: 0.85rem;
    }

    .reading-passage {
      padding: 0.75rem;
    }

    .passage-text {
      font-size: 0.85rem;
      line-height: 1.5;
    }

    .reading-option {
      padding: 0.5rem 0.6rem;
    }

    .reading-option .option-text {
      font-size: 0.85rem;
    }
  }

  .word-bank {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
  }

  .word-tile {
    background: #ffffff;
    border: 2px solid #3b82f6;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: #1e40af;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
    touch-action: manipulation;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile responsive word tiles */
  @media (max-width: 768px) {
    .word-tile {
      padding: 0.75rem 1rem;
      font-size: 1.1rem;
      min-height: 48px;
      cursor: pointer;
    }

    .word-bank {
      gap: 1rem;
      padding: 1.5rem 1rem;
    }
  }

  @media (max-width: 480px) {
    .word-tile {
      padding: 1rem;
      font-size: 1rem;
      min-height: 52px;
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .word-bank {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
  }

  .word-tile:hover {
    background: #eff6ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
  }

  .word-tile.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  .word-tile.touch-active {
    background: #dbeafe;
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  .sentence-builder {
    min-height: 80px;
    background: #ffffff;
    border: 3px dashed #d1d5db;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    transition: all 0.3s ease;
  }

  /* Mobile responsive sentence builder */
  @media (max-width: 768px) {
    .sentence-builder {
      min-height: 100px;
      padding: 1.5rem 1rem;
      gap: 0.75rem;
    }

    .sentence-word {
      padding: 0.75rem 1rem;
      font-size: 1.1rem;
      min-height: 44px;
      display: flex;
      align-items: center;
    }
  }

  @media (max-width: 480px) {
    .sentence-builder {
      min-height: 120px;
      padding: 1.5rem 1rem;
      gap: 1rem;
      justify-content: center;
    }

    .sentence-word {
      padding: 1rem;
      font-size: 1rem;
      min-height: 48px;
      flex: 0 0 auto;
    }

    .drop-zone {
      font-size: 1.1rem;
      padding: 1rem;
    }
  }

  .sentence-builder.drag-over {
    border-color: #3b82f6;
    background: #f0f9ff;
  }

  .drop-zone {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    width: 100%;
  }

  .sentence-word {
    background: #dcfce7;
    border: 2px solid #4ade80;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    color: #16a34a;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .sentence-word:hover {
    background: #bbf7d0;
    transform: translateY(-1px);
  }

  .reset-btn {
    background: #f3f4f6;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .reset-btn:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
  }

  /* Essay container transition styles */
  .english-question-container {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  /* Ensure proper viewport handling on mobile */
  @media (max-width: 768px) {
    .english-assessment-wrapper {
      padding: 1rem;
    }

    .english-header h2 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    .english-header p {
      font-size: 1rem;
      line-height: 1.5;
    }

    .timer-container {
      margin: 1.5rem 0;
    }

    .timer-display {
      padding: 1rem;
    }

    .english-question-container {
      max-height: calc(100vh - 180px);
      min-height: 350px;
      padding: 1rem;
    }

    .english-textarea {
      min-height: 120px;
      max-height: 250px;
    }

    .english-actions {
      padding-top: 1rem;
      margin-top: auto;
      flex-shrink: 0;
    }
  }

  @media (max-width: 480px) {
    .english-assessment-wrapper {
      padding: 0.75rem;
    }

    .english-header h2 {
      font-size: 1.3rem;
    }

    .english-header p {
      font-size: 0.95rem;
    }

    .timer-container {
      margin: 1rem 0;
    }

    .english-question-container {
      max-height: calc(100vh - 160px);
      min-height: 300px;
      padding: 0.75rem;
    }

    .english-textarea {
      min-height: 100px;
      max-height: 200px;
      font-size: 1rem;
    }

    .english-actions {
      padding-top: 0.75rem;
      margin-top: auto;
    }

    .english-submit-btn {
      width: 100%;
      padding: 1rem;
      font-size: 1rem;
    }
  }

  /* Mobile Responsive for English Assessment */
  @media (max-width: 768px) {
    #english-assessment-container,
    #english-success-container,
    #english-completion-container {
      padding: 0.5rem;
    }

    .results-wrapper {
      max-width: 100%;
      max-height: 98vh;
    }

    .results-header {
      padding: 1rem 0.75rem;
    }

    .results-header h2 {
      font-size: 1.1rem;
      margin-bottom: 0.5rem;
    }

    .score-value {
      font-size: 1.75rem;
    }

    .score-level {
      font-size: 0.8rem;
    }

    .results-content {
      padding: 1rem 0.75rem;
    }

    .feedback-section h3 {
      font-size: 1rem;
      margin-bottom: 0.5rem;
    }

    .feedback-grid {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .feedback-item {
      padding: 0.6rem;
    }

    .feedback-item h4 {
      font-size: 0.75rem;
    }

    .feedback-item p {
      font-size: 0.8rem;
    }

    .overall-feedback {
      padding: 0.75rem;
    }

    .overall-feedback h4 {
      font-size: 0.85rem;
    }

    .overall-feedback p {
      font-size: 0.8rem;
    }

    .strengths-improvements {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .strengths-section,
    .improvements-section {
      padding: 0.75rem;
    }

    .strengths-section h4,
    .improvements-section h4 {
      font-size: 0.85rem;
      margin-bottom: 0.5rem;
    }

    .strengths-section li,
    .improvements-section li {
      font-size: 0.75rem;
      margin-bottom: 0.3rem;
    }

    .next-steps {
      padding: 0.75rem;
    }

    .next-steps h4 {
      font-size: 0.85rem;
    }

    .next-steps p {
      font-size: 0.8rem;
    }

    .results-actions {
      flex-direction: column;
      gap: 0.5rem;
    }

    .results-btn {
      min-width: auto;
      width: 100%;
      padding: 0.7rem 1rem;
      font-size: 0.85rem;
    }

    .english-assessment-wrapper {
      height: 98vh;
    }

    .english-header,
    .completion-header {
      padding: 1rem;
    }

    .english-header h2,
    .completion-header h2 {
      font-size: 1.125rem;
    }

    .english-header p {
      font-size: 0.8rem;
    }

    .english-question-container,
    .completion-content {
      padding: 1rem;
    }

    .question-text h3 {
      font-size: 1rem;
    }

    .question-text p {
      font-size: 0.8rem;
    }

    .english-textarea {
      min-height: 120px;
      font-size: 0.85rem;
      padding: 0.5rem;
    }

    .timer-display {
      font-size: 0.85rem;
    }

    .timer-value {
      font-size: 1rem;
    }

    .seasons-image {
      max-height: 250px;
    }

    .response-label {
      font-size: 0.85rem;
    }

    .character-count {
      font-size: 0.7rem;
    }

    .english-submit-btn {
      padding: 0.625rem 1.25rem;
      font-size: 0.85rem;
    }
  }

  @media (max-width: 480px) {
    #english-assessment-container,
    #english-success-container,
    #english-completion-container {
      padding: 0.25rem;
    }

    .results-wrapper {
      border-radius: 8px;
      max-height: 99vh;
    }

    .results-header {
      padding: 0.75rem 0.5rem;
    }

    .results-header h2 {
      font-size: 1rem;
      margin-bottom: 0.4rem;
    }

    .score-value {
      font-size: 1.5rem;
    }

    .score-level {
      font-size: 0.75rem;
    }

    .results-content {
      padding: 0.75rem 0.5rem;
    }

    .feedback-section {
      margin-bottom: 1rem;
    }

    .feedback-section h3 {
      font-size: 0.95rem;
      margin-bottom: 0.4rem;
    }

    .feedback-grid {
      gap: 0.4rem;
      margin-bottom: 0.75rem;
    }

    .feedback-item {
      padding: 0.5rem;
    }

    .feedback-item h4 {
      font-size: 0.7rem;
      margin-bottom: 0.3rem;
    }

    .feedback-item p {
      font-size: 0.75rem;
      line-height: 1.3;
    }

    .overall-feedback {
      padding: 0.6rem;
    }

    .overall-feedback h4 {
      font-size: 0.8rem;
      margin-bottom: 0.4rem;
    }

    .overall-feedback p {
      font-size: 0.75rem;
    }

    .strengths-improvements {
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .strengths-section,
    .improvements-section {
      padding: 0.6rem;
    }

    .strengths-section h4,
    .improvements-section h4 {
      font-size: 0.8rem;
      margin-bottom: 0.4rem;
    }

    .strengths-section li,
    .improvements-section li {
      font-size: 0.7rem;
      margin-bottom: 0.25rem;
      line-height: 1.3;
    }

    .next-steps {
      padding: 0.6rem;
      margin-bottom: 1rem;
    }

    .next-steps h4 {
      font-size: 0.8rem;
      margin-bottom: 0.4rem;
    }

    .next-steps p {
      font-size: 0.75rem;
      line-height: 1.4;
    }

    .results-btn {
      padding: 0.6rem 0.8rem;
      font-size: 0.8rem;
    }

    .english-assessment-wrapper {
      height: 99vh;
      border-radius: 12px;
    }

    .english-header,
    .completion-header {
      padding: 0.75rem;
    }

    .english-header h2,
    .completion-header h2 {
      font-size: 1rem;
    }

    .english-header p {
      font-size: 0.75rem;
    }

    .english-question-container,
    .completion-content {
      padding: 0.75rem;
    }

    .question-text h3 {
      font-size: 0.95rem;
    }

    .question-text p {
      font-size: 0.75rem;
    }

    .english-textarea {
      min-height: 100px;
      font-size: 0.8rem;
      padding: 0.5rem;
    }

    .english-submit-btn,
    .completion-btn {
      width: 100%;
      justify-content: center;
      padding: 0.75rem;
      font-size: 0.8rem;
    }

    .timer-container {
      padding: 0.5rem;
    }

    .timer-display {
      font-size: 0.8rem;
    }

    .timer-value {
      font-size: 0.9rem;
      padding: 0.2rem 0.4rem;
    }

    .seasons-image {
      max-height: 200px;
    }

    .visual-aid-container {
      margin-bottom: 1rem;
      padding: 0 0.5rem;
    }

    .question-text {
      margin-bottom: 0.75rem;
    }

    .response-label {
      font-size: 0.8rem;
      margin-bottom: 0.25rem;
    }

    .character-count {
      font-size: 0.65rem;
      margin-top: 0.25rem;
    }

    .english-actions {
      padding-top: 0.75rem;
    }
  }

  /* Landscape mobile and very small screens fallback */
  @media (max-height: 600px) and (orientation: landscape) {
    .english-assessment-wrapper {
      height: auto;
      max-height: 95vh;
      overflow-y: auto;
    }

    .results-wrapper {
      max-height: 95vh;
    }

    .results-header {
      padding: 0.75rem 1rem;
    }

    .results-header h2 {
      font-size: 1rem;
      margin-bottom: 0.4rem;
    }

    .score-value {
      font-size: 1.5rem;
    }

    .results-content {
      padding: 0.75rem 1rem;
    }

    .feedback-section {
      margin-bottom: 0.75rem;
    }

    .feedback-section h3 {
      font-size: 0.95rem;
      margin-bottom: 0.4rem;
    }

    .strengths-improvements {
      margin-bottom: 0.75rem;
    }

    .next-steps {
      margin-bottom: 0.75rem;
    }

    .english-question-container {
      overflow-y: auto;
    }

    .english-textarea {
      min-height: 80px;
    }

    .seasons-image {
      max-height: 150px;
    }

    .english-header {
      padding: 0.5rem 1rem;
    }

    .english-header h2 {
      font-size: 1rem;
    }

    .english-header p {
      font-size: 0.75rem;
    }

    .timer-container {
      padding: 0.5rem;
    }

    .question-text {
      margin-bottom: 0.5rem;
    }

    .visual-aid-container {
      margin-bottom: 0.75rem;
      padding: 0 0.25rem;
    }
  }

  /* Very small height screens */
  @media (max-height: 500px) {
    #english-assessment-container,
    #english-success-container,
    #english-completion-container {
      align-items: flex-start;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }

    .english-assessment-wrapper,
    .results-wrapper {
      height: auto;
      max-height: calc(100vh - 1rem);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .results-header {
      padding: 0.5rem 0.75rem;
    }

    .results-header h2 {
      font-size: 0.95rem;
      margin-bottom: 0.3rem;
    }

    .score-value {
      font-size: 1.25rem;
    }

    .score-level {
      font-size: 0.7rem;
    }

    .results-content {
      padding: 0.5rem 0.75rem;
    }

    .feedback-section {
      margin-bottom: 0.5rem;
    }

    .feedback-section h3 {
      font-size: 0.85rem;
      margin-bottom: 0.3rem;
    }

    .strengths-improvements {
      margin-bottom: 0.5rem;
    }

    .next-steps {
      margin-bottom: 0.5rem;
    }

    .english-textarea {
      min-height: 60px;
    }

    .seasons-image {
      max-height: 120px;
    }
  }

/* ============================================================================
   MATHEMATICS ASSESSMENT STYLES
   ============================================================================ */

/* Mathematics Assessment Container */
#math-assessment-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
  position: relative;
  z-index: 1;
}

/* Smooth scrolling - scoped to mathematics assessment only */
#math-assessment-container {
  scroll-behavior: smooth;
}

/* Focus management for accessibility - scoped to math assessment */
#math-assessment-container .results-container:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}




#math-assessment-container .assessment-screen {
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}

/* Assessment Instructions - Scoped to Math Assessment */
#math-assessment-container .instruction-container {
  padding: 2rem;
  text-align: center;
}

#math-assessment-container .instruction-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

#math-assessment-container .instruction-content {
  margin-bottom: 2rem;
}

#math-assessment-container .instruction-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

#math-assessment-container .instruction-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

#math-assessment-container .instruction-item p {
  margin: 0;
  font-size: 1rem;
  color: #374151;
  text-align: left;
}

/* Question Container - Scoped to Math Assessment */
#math-assessment-container .question-container {
  padding: 2rem;
  min-height: 500px;
  max-height: 90vh; /* Prevent container from exceeding viewport */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent container overflow */
}

#math-assessment-container .question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

#math-assessment-container .question-progress {
  flex: 1;
}

#math-assessment-container .progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

#math-assessment-container .progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.timer-container {
  background: #fef3c7;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid #f59e0b;
}

.timer-container span {
  font-size: 1.125rem;
  font-weight: 600;
  color: #92400e;
}

/* Question Content */
.question-content {
  flex: 1;
  margin-bottom: 2rem;
}

.question-topic {
  margin-bottom: 0.5rem;
}

.question-topic span {
  display: inline-block;
  background: #dbeafe;
  color: #1d4ed8;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.question-content {
  flex: 1; /* Take up available space */
  overflow-y: auto; /* Allow scrolling */
  margin-bottom: 1rem; /* Space before actions */
  padding-right: 0.5rem; /* Space for scrollbar */
}

.question-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

/* Answer Options */
.answer-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.option-btn {
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-btn:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.option-btn.selected {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
  font-weight: 600;
}

/* Answer Inputs */
.answer-input {
  margin-bottom: 1.5rem;
}

.numeric-input,
.text-input {
  width: 100%;
  padding: 1rem;
  font-size: 1.125rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
  color: #374151;
  transition: all 0.3s ease;
}

.numeric-input:focus,
.text-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-hint {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Interactive Question Types */
.interactive-question {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  max-height: 70vh; /* Ensure it doesn't exceed viewport */
  overflow-y: auto; /* Allow scrolling if needed */
  position: relative; /* For proper positioning of child elements */
}

/* Number Line Slider */
.number-line-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
}

.number-line-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.number-line-track {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: 8px;
  background: linear-gradient(to right, #e5e7eb 0%, #d1d5db 50%, #e5e7eb 100%);
  border-radius: 4px;
  cursor: pointer;
}

.number-line-handle {
  position: absolute;
  top: -8px;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  border: 3px solid #ffffff;
  border-radius: 50%;
  cursor: grab;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  transform: translateX(-50%);
}

.number-line-handle:hover,
.number-line-handle:focus {
  background: #2563eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.number-line-handle:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.number-line-handle:active {
  cursor: grabbing;
  transform: translateX(-50%) scale(1.1);
}

.number-line-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  background: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  min-width: 60px;
  text-align: center;
}

/* Drag and Drop Matching */
.matching-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
  max-height: 60vh; /* Limit height to prevent viewport overflow */
  overflow-y: auto; /* Allow scrolling if content is too large */
}

.draggable-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  padding: 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  min-height: 80px;
  max-height: 25vh; /* Limit height */
  overflow-y: auto; /* Allow scrolling for many items */
}

.draggable-item {
  background: #3b82f6;
  color: #ffffff;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: grab;
  font-weight: 500;
  transition: all 0.2s ease;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.draggable-item:hover,
.draggable-item:focus {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

.draggable-item:focus-visible {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

/* Mobile touch improvements */
@media (hover: none) and (pointer: coarse) {
  .draggable-item {
    padding: 0.875rem 1.25rem; /* Larger touch targets */
    font-size: 1rem; /* Larger text for readability */
  }

  .drop-zone {
    min-height: 80px; /* Larger drop zones */
    font-size: 1rem;
  }

  .reset-btn {
    padding: 0.75rem 1.5rem; /* Larger touch target */
    font-size: 1rem;
  }
}

.draggable-item:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.draggable-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drop-zones {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  max-height: 30vh; /* Limit height */
  overflow-y: auto; /* Allow scrolling for many zones */
}

.drop-zone {
  min-height: 80px;
  background: #f3f4f6;
  border: 2px dashed #9ca3af;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.drop-zone.drag-over {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.drop-zone.filled {
  background: #dcfce7;
  border-color: #16a34a;
  color: #15803d;
}

.drop-zone-label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

/* Interactive Controls */
.interactive-controls {
  display: flex;
  justify-content: center;
  margin: 1rem 0 0.5rem 0;
  gap: 0.75rem;
}

.reset-btn {
  background: #f59e0b;
  color: #ffffff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reset-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reset-btn:active {
  transform: translateY(0);
}

.reset-btn .btn-icon {
  font-size: 1rem;
  font-weight: bold;
}

/* Question Actions */
.question-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0; /* Prevent shrinking */
  margin-top: auto; /* Push to bottom */
  padding-top: 1rem; /* Space from content */
  border-top: 1px solid #e5e7eb; /* Visual separator */
  background: #ffffff; /* Ensure visibility */
}

/* Area Models */
.area-model-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem 0;
}

.fraction-bars {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.fraction-bar {
  display: flex;
  border: 2px solid #374151;
  border-radius: 4px;
  overflow: hidden;
  background: #ffffff;
}

.fraction-segment {
  height: 40px;
  border-right: 1px solid #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.fraction-segment:last-child {
  border-right: none;
}

.fraction-segment:hover {
  background: #f3f4f6;
}

.fraction-segment.shaded {
  background: #3b82f6;
  color: #ffffff;
}

.geometric-shapes {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.geometric-shape {
  cursor: pointer;
  transition: all 0.2s ease;
  stroke: #374151;
  stroke-width: 2;
  fill: #ffffff;
}

.geometric-shape:hover {
  fill: #f3f4f6;
  stroke: #1f2937;
}

.geometric-shape.shaded {
  fill: #3b82f6;
  stroke: #1e40af;
}

/* Detailed Report Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s ease;
}

.modal-overlay.show .modal-container {
  transform: scale(1) translateY(0);
}

.modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.modal-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: #ffffff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.5rem;
  font-weight: bold;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-content {
  padding: 2rem;
  max-height: calc(90vh - 200px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Report Sections */
.report-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  background: #ffffff;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  font-weight: 500;
  color: #64748b;
  font-size: 0.875rem;
}

.summary-value {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.summary-value.status-passed {
  color: #059669;
}

.summary-value.status-complete {
  color: #3b82f6;
}

.summary-value.status-progress {
  color: #d97706;
}

.topic-breakdown-detailed {
  display: grid;
  gap: 1rem;
}

.topic-item-detailed {
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.topic-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.topic-score {
  font-weight: 600;
  color: #3b82f6;
  font-size: 1rem;
}

.topic-progress {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.topic-progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transition: width 0.5s ease;
}

.topic-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.feedback-detailed {
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.feedback-item {
  margin-bottom: 1.5rem;
}

.feedback-item:last-child {
  margin-bottom: 0;
}

.feedback-category {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.feedback-text {
  color: #64748b;
  line-height: 1.6;
  font-size: 0.9375rem;
}

.strengths-list,
.improvements-list {
  display: grid;
  gap: 0.75rem;
}

.strength-item,
.improvement-item {
  background: #ffffff;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.improvement-item {
  border-left-color: #f59e0b;
}

.item-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.item-text {
  color: #374151;
  line-height: 1.5;
  font-size: 0.9375rem;
}

.recommendations-detailed,
.courses-list {
  display: grid;
  gap: 1rem;
}

.recommendation-item,
.course-item {
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.recommendation-title,
.course-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.recommendation-description,
.course-description {
  color: #64748b;
  line-height: 1.5;
  font-size: 0.9375rem;
}

.modal-footer {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.modal-action-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
}

.modal-action-btn.primary {
  background: #3b82f6;
  color: #ffffff;
}

.modal-action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.modal-action-btn.secondary {
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
}

.modal-action-btn.secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.skip-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 2px solid #e5e7eb;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skip-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.next-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.next-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.next-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Results Container */
.results-container {
  padding: 1.5rem;
  text-align: center;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.results-container::-webkit-scrollbar {
  width: 8px;
}

.results-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.results-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.results-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.results-header {
  flex-shrink: 0;
  margin-bottom: 0;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.results-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.results-score {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  flex-wrap: wrap;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.score-label {
  color: #e0e7ff;
  font-weight: 500;
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.score-total {
  color: #e0e7ff;
  font-weight: 500;
}

/* Results Content Area */
.results-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.result-status {
  margin-bottom: 0;
}

.status-badge {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Passed Status */
.status-badge.passed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  border: 2px solid #10b981;
}

/* Achievement Level Styles */
.status-badge.excellent-progress {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
  border: 2px solid #8b5cf6;
}

.status-badge.good-progress {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: #ffffff;
  border: 2px solid #3b82f6;
}

.status-badge.steady-progress {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  border: 2px solid #f59e0b;
}

.status-badge.foundation-building {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: #ffffff;
  border: 2px solid #06b6d4;
}

.status-badge.getting-started {
  background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
  color: #ffffff;
  border: 2px solid #84cc16;
}

/* Encouraging Message Styles */
.encouraging-message {
  margin-top: 1rem;
  padding: 0;
}

.message-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.message-text {
  margin: 0;
  color: #374151;
  font-weight: 500;
  font-size: 0.95rem;
  line-height: 1.4;
  text-align: center;
}

/* Feedback and Recommendations */
.feedback-section,
.recommendations-section {
  margin-bottom: 0;
  text-align: left;
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.feedback-section h3,
.recommendations-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.topic-breakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.topic-name {
  font-weight: 500;
  color: #374151;
}

.topic-score {
  font-weight: 600;
  color: #3b82f6;
}

.recommendations {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.recommendation-level {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.recommendation-reasoning {
  margin-bottom: 1rem;
  color: #374151;
  line-height: 1.6;
}

.recommendation-next-steps ul {
  margin: 0.5rem 0 0 1rem;
  color: #374151;
}

.recommendation-next-steps li {
  margin-bottom: 0.5rem;
}

/* Enhanced Loading Screen for Mathematics Assessment */
.loading-container {
  padding: 3rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.loading-animation {
  margin-bottom: 2rem;
}

/* Enhanced Spinner Animation */
.enhanced-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: rotate 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #3b82f6;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #10b981;
  animation-delay: -0.5s;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #f59e0b;
  animation-delay: -1s;
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fallback spinner for older browsers */
.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-text {
  color: #4b5563;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 2rem;
  animation: pulse-text 2s ease-in-out infinite;
  transition: opacity 0.3s ease-in-out;
  min-height: 1.5rem; /* Prevent layout shift during text changes */
}

@keyframes pulse-text {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Progress Container */
.progress-container {
  margin-top: 2rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
  border-radius: 3px;
  animation: progress-animation 3s ease-in-out infinite;
}

@keyframes progress-animation {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6b7280;
}

.step {
  position: relative;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.step.active {
  color: #3b82f6;
  font-weight: 600;
  background-color: rgba(59, 130, 246, 0.1);
}

.step.completed {
  color: #10b981;
  font-weight: 500;
}

/* Smooth fade-in animation for loading screen */
#assessment-loading {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Mathematics Assessment Mobile Responsive */
@media (max-width: 768px) {
  #math-assessment-container {
    padding: 0.5rem;
  }

  .assessment-screen {
    border-radius: 12px;
    max-height: 95vh;
  }

  .instruction-container,
  .question-container {
    padding: 1.5rem;
  }

  .results-container {
    padding: 1rem;
    gap: 1rem;
  }

  .instruction-title,
  .results-title {
    font-size: 1.25rem;
  }

  .results-score {
    font-size: 1rem;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.875rem 1.25rem;
  }

  .score-value {
    font-size: 1.5rem;
  }

  .status-badge {
    padding: 0.6rem 1.25rem;
    font-size: 0.9rem;
  }

  .encouraging-message {
    margin-top: 0.75rem;
  }

  .message-content {
    padding: 0.875rem 1.25rem;
    gap: 0.5rem;
  }

  .message-icon {
    font-size: 1.25rem;
  }

  .message-text {
    font-size: 0.875rem;
  }

  .question-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .answer-options {
    grid-template-columns: 1fr;
  }

  .question-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .skip-btn,
  .next-btn {
    width: 100%;
    justify-content: center;
  }

  .topic-breakdown {
    grid-template-columns: 1fr;
  }

  .results-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .results-actions button {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  /* Enhanced feedback sections for mobile */
  .feedback-section,
  .recommendations-section {
    margin-bottom: 0;
  }

  .feedback-section h3,
  .recommendations-section h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .topic-item {
    padding: 0.75rem;
  }

  .topic-name {
    font-size: 0.8rem;
  }

  .topic-percentage {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }

  .recommendations {
    padding: 1rem;
  }

  .recommendation-content {
    gap: 1rem;
  }

  .course-item {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  /* Interactive Question Types Mobile */
  .interactive-question {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .number-line-container {
    padding: 0.75rem 0;
    gap: 0.75rem;
  }

  .number-line-track {
    max-width: 300px;
  }

  .number-line-handle {
    width: 20px;
    height: 20px;
    top: -6px;
  }

  .number-line-value {
    font-size: 1rem;
    padding: 0.4rem 0.8rem;
    min-width: 50px;
  }

  .matching-container {
    gap: 1rem;
    padding: 0.75rem 0;
    max-height: 65vh; /* Slightly reduce on mobile */
  }

  .draggable-items {
    padding: 0.75rem;
    gap: 0.5rem;
    min-height: 60px;
    max-height: 20vh; /* Reduce height on mobile */
  }

  .draggable-item {
    padding: 0.6rem 0.8rem;
    font-size: 0.875rem;
  }

  .drop-zones {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    max-height: 35vh; /* Reduce height on mobile */
  }

  .drop-zone {
    min-height: 60px;
    font-size: 0.875rem;
  }

  .area-model-container {
    gap: 1.5rem;
    padding: 0.75rem 0;
  }

  .fraction-segment {
    height: 35px;
    font-size: 0.7rem;
  }

  .geometric-shapes {
    gap: 0.75rem;
  }

  /* Detailed Report Modal Mobile */
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-container {
    max-height: 95vh;
    border-radius: 12px;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .modal-content {
    padding: 1.5rem;
    max-height: calc(95vh - 160px);
  }

  .report-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
  }

  .section-title {
    font-size: 1.125rem;
  }

  .summary-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .summary-item {
    padding: 0.75rem;
  }

  .topic-item-detailed {
    padding: 1rem;
  }

  .feedback-detailed {
    padding: 1rem;
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-action-btn {
    width: 100%;
    justify-content: center;
    padding: 0.875rem 1rem;
  }
}

@media (max-width: 480px) {
  #math-assessment-container {
    padding: 0.25rem;
  }

  .assessment-screen {
    max-height: 98vh;
    border-radius: 8px;
  }

  .instruction-container,
  .question-container {
    padding: 1rem;
  }

  .results-container {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .instruction-title,
  .results-title {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .question-text {
    font-size: 1rem;
  }

  .instruction-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .instruction-item p {
    text-align: center;
    font-size: 0.8rem;
  }

  .numeric-input,
  .text-input {
    font-size: 1rem;
    padding: 0.75rem;
  }

  .results-score {
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
  }

  .score-value {
    font-size: 1.5rem;
  }

  .status-badge {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .encouraging-message {
    margin-top: 0.5rem;
  }

  .message-content {
    padding: 0.75rem 1rem;
    gap: 0.4rem;
    flex-direction: column;
    text-align: center;
  }

  .message-icon {
    font-size: 1.125rem;
  }

  .message-text {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .topic-breakdown {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .topic-item {
    padding: 0.5rem;
  }

  .topic-name {
    font-size: 0.75rem;
  }

  .topic-percentage {
    font-size: 0.7rem;
    padding: 0.15rem 0.3rem;
  }

  .feedback-section h3,
  .recommendations-section h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .feedback-item {
    padding: 0.75rem;
  }

  .feedback-title {
    font-size: 0.75rem;
  }

  .feedback-text {
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .recommendations {
    padding: 0.75rem;
  }

  .recommendation-level {
    margin-bottom: 1rem;
  }

  .level-badge {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .level-label {
    font-size: 0.75rem;
  }

  .course-item {
    padding: 0.4rem;
    font-size: 0.8rem;
  }

  .results-actions button {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .btn-text {
    font-size: 0.8rem;
  }

  .btn-icon {
    font-size: 0.9rem;
  }
}

/* Mathematics Assessment Progression Buttons */
.results-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0;
  padding-top: 1rem;
  border-top: 2px solid #e5e7eb;
  flex-shrink: 0;
}

.progression-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* ============================================================================
   CALCULATOR WIDGET STYLES
   ============================================================================ */

/* Calculator Widget Container */
.calculator-widget {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Montserrat', sans-serif;
}

/* Calculator Toggle Button */
.calculator-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  user-select: none;
}

.calculator-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.calculator-toggle-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.calculator-toggle-btn:focus {
  outline: 2px solid #fbbf24;
  outline-offset: 2px;
}

.calculator-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2.5;
}

.calculator-label {
  font-size: 0.875rem;
  font-weight: 600;
}

/* Calculator Modal */
.calculator-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Calculator Container - Casio Style with Scrolling */
.calculator-container {
  background: #2c3e50; /* Dark blue-gray like Casio calculators */
  border-radius: 8px; /* Less rounded, more rectangular */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border: 2px solid #34495e;
  width: 100%;
  max-width: 320px; /* Compact width like physical Casio */
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  cursor: move;
  user-select: none;
  font-family: 'Arial', sans-serif; /* Clean, technical font */
  display: flex;
  flex-direction: column;
}

.calculator-container.dragging {
  cursor: grabbing;
  transform: rotate(2deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

/* Fixed Header and Display Section */
.calculator-fixed-section {
  flex-shrink: 0; /* Never shrink */
  background: #2c3e50;
  border-radius: 8px 8px 0 0;
}

/* Scrollable Button Section */
.calculator-scrollable-section {
  flex: 1; /* Take remaining space */
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background: #1a252f;
  border-radius: 0 0 8px 8px;
  /* Smooth scrolling */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
}

/* Custom scrollbar for webkit browsers */
.calculator-scrollable-section::-webkit-scrollbar {
  width: 6px;
}

.calculator-scrollable-section::-webkit-scrollbar-track {
  background: #1a252f;
  border-radius: 3px;
}

.calculator-scrollable-section::-webkit-scrollbar-thumb {
  background: #34495e;
  border-radius: 3px;
}

.calculator-scrollable-section::-webkit-scrollbar-thumb:hover {
  background: #4a5f7a;
}

/* Scroll Indicators */
.scroll-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 8px;
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scroll-indicator-top {
  top: 0;
  background: linear-gradient(to bottom, rgba(26, 37, 47, 0.9) 0%, rgba(26, 37, 47, 0) 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scroll-indicator-bottom {
  bottom: 0;
  background: linear-gradient(to top, rgba(26, 37, 47, 0.9) 0%, rgba(26, 37, 47, 0) 100%);
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.3);
}

.scroll-indicator.visible {
  opacity: 1;
}

/* Calculator Header */
.calculator-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  cursor: move;
}

.calculator-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.calculator-close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calculator-close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.calculator-close-btn:focus {
  outline: 2px solid #fbbf24;
  outline-offset: 2px;
}

.calculator-close-btn svg {
  width: 16px;
  height: 16px;
}

/* Calculator Display - Casio Style */
.calculator-display {
  padding: 1rem 1rem 0.5rem 1rem;
  background: #2c3e50;
  border-bottom: 1px solid #1a252f;
}

.calculator-screen {
  background: #0f1419; /* Dark LCD-like background */
  color: #00ff41; /* Green LCD text like classic Casio */
  font-family: 'Courier New', 'Monaco', monospace;
  font-size: 1.4rem;
  font-weight: 400;
  text-align: right;
  padding: 0.8rem 1rem;
  border-radius: 4px;
  border: 2px inset #34495e; /* Inset border for LCD effect */
  min-height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  word-break: break-all;
  overflow-wrap: break-word;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Calculator Buttons Grid - Casio Layout with Scrolling */
.calculator-buttons.casio-layout {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
  background: #1a252f;
  padding: 1rem;
  min-height: min-content; /* Allow natural height */
}

/* Casio Calculator Button Base Styles */
.calc-btn {
  border: 1px solid #34495e;
  padding: 0.8rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 45px;
  user-select: none;
  font-family: 'Arial', sans-serif;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.calc-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.calc-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.calc-btn:focus {
  outline: 2px solid #3498db;
  outline-offset: -2px;
  z-index: 1;
}

/* Casio Number Buttons - Light gray like physical Casio */
.casio-number {
  background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
  color: #2c3e50;
  border: 1px solid #95a5a6;
  font-weight: 600;
  font-size: 1.1rem;
}

.casio-number:hover {
  background: linear-gradient(135deg, #d5dbdb 0%, #a6acaf 100%);
}

.casio-number:active {
  background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
}

/* Zero Button (spans 2 columns) */
.casio-zero {
  grid-column: span 2;
}

/* Casio Operation Buttons - Orange like physical Casio */
.casio-operation {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
}

.casio-operation:hover {
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.casio-operation:active {
  background: linear-gradient(135deg, #d35400 0%, #ba4a00 100%);
}

/* Casio Scientific Function Buttons - Dark blue like physical Casio */
.casio-function {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  color: #ecf0f1;
  font-size: 0.8rem;
  font-weight: 500;
}

.casio-function:hover {
  background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
}

.casio-function:active {
  background: linear-gradient(135deg, #1a252f 0%, #0f1419 100%);
}

/* Casio Memory Buttons - Purple like physical Casio */
.casio-memory {
  background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
}

.casio-memory:hover {
  background: linear-gradient(135deg, #7d3c98 0%, #6c3483 100%);
}

.casio-memory:active {
  background: linear-gradient(135deg, #6c3483 0%, #5b2c6f 100%);
}

/* Casio Clear Buttons - Red like physical Casio */
.casio-clear {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
}

.casio-clear:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

.casio-clear:active {
  background: linear-gradient(135deg, #a93226 0%, #922b21 100%);
}

/* Casio Equals Button - Blue like physical Casio */
.casio-equals {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  font-size: 1.3rem;
  font-weight: 700;
  grid-column: span 4; /* Full width like many Casio calculators */
}

.casio-equals:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
}

.casio-equals:active {
  background: linear-gradient(135deg, #1f618d 0%, #154360 100%);
}

/* Special styling for mathematical symbols in Casio style */
.casio-function[data-function="sqrt"] {
  font-size: 1rem;
}

.casio-function[data-function="square"] {
  font-size: 0.85rem;
}

.casio-function[data-function="power"] {
  font-size: 0.8rem;
}

/* Parentheses buttons - same as function buttons but slightly larger text */
.casio-function[data-action="parenthesis-open"],
.casio-function[data-action="parenthesis-close"] {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Calculator Widget Responsive Design with Scrolling */
@media (max-width: 768px) {
  .calculator-widget {
    top: 10px;
    right: 10px;
  }

  .calculator-toggle-btn {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
  }

  .calculator-icon {
    width: 16px;
    height: 16px;
  }

  .calculator-label {
    font-size: 0.8rem;
  }

  .calculator-modal {
    padding: 0.5rem;
  }

  .calculator-container {
    max-width: 300px; /* Compact Casio size */
    max-height: 85vh;
  }

  /* Ensure scrollable section has proper height on mobile */
  .calculator-scrollable-section {
    max-height: calc(85vh - 140px); /* Account for header and display */
  }

  .calculator-header {
    padding: 0.875rem 1rem;
  }

  .calculator-title {
    font-size: 0.875rem;
  }

  .calculator-display {
    padding: 1.25rem 1rem;
  }

  .calculator-screen {
    font-size: 1.25rem;
    padding: 0.625rem 0.875rem;
    min-height: 2.25rem;
  }

  .calc-btn {
    padding: 0.875rem;
    font-size: 1rem;
    min-height: 50px;
  }

  .calc-btn-operation,
  .calc-btn-equals {
    font-size: 1.125rem;
  }

  .calc-btn-clear {
    font-size: 0.875rem;
  }

  .casio-function {
    font-size: 0.7rem;
    padding: 0.7rem;
  }

  .casio-memory {
    font-size: 0.75rem;
    padding: 0.7rem;
  }

  .casio-operation {
    font-size: 1rem;
  }

  .casio-number {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .calculator-widget {
    top: 5px;
    right: 5px;
  }

  .calculator-toggle-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 10px;
  }

  .calculator-icon {
    width: 14px;
    height: 14px;
  }

  .calculator-label {
    font-size: 0.75rem;
  }

  .calculator-container {
    max-width: 280px; /* Compact Casio size for small mobile */
    max-height: 90vh;
  }

  /* Ensure scrollable section has proper height on small mobile */
  .calculator-scrollable-section {
    max-height: calc(90vh - 130px); /* Account for header and display on small screens */
  }

  .calculator-header {
    padding: 0.75rem 0.875rem;
  }

  .calculator-title {
    font-size: 0.8rem;
  }

  .calculator-display {
    padding: 1rem 0.875rem;
  }

  .calculator-screen {
    font-size: 1.125rem;
    padding: 0.5rem 0.75rem;
    min-height: 2rem;
  }

  .calc-btn {
    padding: 0.75rem;
    font-size: 0.875rem;
    min-height: 45px;
  }

  .calc-btn-operation,
  .calc-btn-equals {
    font-size: 1rem;
  }

  .calc-btn-clear {
    font-size: 0.8rem;
  }

  .casio-function {
    font-size: 0.65rem;
    padding: 0.6rem;
  }

  .casio-memory {
    font-size: 0.7rem;
    padding: 0.6rem;
  }

  .casio-operation {
    font-size: 0.95rem;
  }

  .casio-number {
    font-size: 0.95rem;
  }

  .casio-equals {
    font-size: 1.1rem;
  }
}

/* Calculator Animation States */
.calculator-modal.show {
  animation: calculatorFadeIn 0.3s ease-out;
}

.calculator-modal.hide {
  animation: calculatorFadeOut 0.2s ease-in;
}

.calculator-container.show {
  animation: calculatorSlideIn 0.3s ease-out;
}

@keyframes calculatorFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes calculatorFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes calculatorSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Calculator Error State */
.calculator-screen.error {
  color: #ef4444;
  animation: calculatorShake 0.5s ease-in-out;
}

@keyframes calculatorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Calculator Button Press Animation */
.calc-btn.pressed {
  animation: buttonPress 0.1s ease-in-out;
}

@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.progression-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.retake-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.retake-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

@media (min-width: 768px) {
  .results-actions {
    flex-direction: row;
    justify-content: center;
  }

  .results-actions button {
    flex: 1;
    max-width: 250px;
  }
}

/* Enhanced Topic Breakdown Styles */
.topic-breakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 0;
}

.topic-item {
  display: flex;
  flex-direction: column;
  padding: 0.875rem;
  background: #ffffff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  gap: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.topic-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.topic-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.topic-percentage {
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.topic-percentage.high {
  background: #d1fae5;
  color: #065f46;
}

.topic-percentage.medium {
  background: #fef3c7;
  color: #92400e;
}

.topic-percentage.low {
  background: #fee2e2;
  color: #991b1b;
}

.topic-progress-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.topic-progress-fill {
  height: 100%;
  transition: width 0.8s ease;
  border-radius: 3px;
}

.topic-progress-fill.high {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.topic-progress-fill.medium {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.topic-progress-fill.low {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.topic-score {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: right;
}

/* Enhanced Recommendations Styles */
.recommendation-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.recommendation-level {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.level-badge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1.125rem;
}

.level-label {
  color: #6b7280;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.recommendation-content {
  display: grid;
  gap: 1.5rem;
}

.recommendation-reasoning h4,
.recommendation-next-steps h4,
.recommendation-courses h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.recommendation-reasoning h4::before {
  content: "📋";
}

.recommendation-next-steps h4::before {
  content: "🎯";
}

.recommendation-courses h4::before {
  content: "📚";
}

.next-steps-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.next-steps-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  padding-left: 1.5rem;
}

.next-steps-list li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: #3b82f6;
  font-weight: 600;
}

.next-steps-list li:last-child {
  border-bottom: none;
}

.course-list {
  display: grid;
  gap: 0.75rem;
}

.course-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.course-item:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.course-icon {
  font-size: 1.25rem;
}

.course-name {
  font-weight: 500;
  color: #374151;
}

/* Enhanced Feedback Styles */
.feedback-container {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.feedback-item {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
}

.feedback-item.overall-feedback {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.feedback-icon {
  font-size: 1.125rem;
}

.feedback-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.feedback-text {
  color: #374151;
  line-height: 1.5;
  margin: 0;
  font-size: 0.875rem;
}

/* Strengths and Improvements Styles */
.strengths-section,
.improvements-section {
  margin-top: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title.positive {
  color: #065f46;
}

.section-title.improvement {
  color: #92400e;
}

.strengths-list,
.improvements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 0.5rem;
}

.strength-item,
.improvement-item {
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.4;
}

.strength-item {
  background: #d1fae5;
  color: #065f46;
  border-left: 3px solid #10b981;
}

.improvement-item {
  background: #fef3c7;
  color: #92400e;
  border-left: 3px solid #f59e0b;
}

/* ============================================================================
   ADMIN DASHBOARD STYLES
   ============================================================================ */

/* Assessment Tab Styles */
.assessment-tab {
  color: #6b7280;
  border-bottom-color: transparent;
  transition: all 0.3s ease;
}

.assessment-tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.assessment-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* Assessment Panel Styles */
.assessment-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chart Container Styles */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Modal Styles */
#assessment-modal {
  backdrop-filter: blur(4px);
}

/* Table Hover Effects */
.assessment-table tbody tr:hover {
  background-color: #f9fafb;
}

/* Stats Card Hover Effects */
.stats-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button Styles for Admin Dashboard */
.admin-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.admin-btn:active {
  transform: translateY(0);
}

/* Loading Spinner for Admin Dashboard */
.admin-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design for Admin Dashboard */
@media (max-width: 768px) {
  .assessment-tab {
    font-size: 0.75rem;
    padding: 0.5rem 0.25rem;
  }

  .stats-card {
    padding: 1rem;
  }

  .chart-container {
    height: 250px;
  }

  #assessment-modal .max-w-4xl {
    max-width: 95vw;
    margin: 1rem;
  }
}

/* Enhanced Next Question Button Styles for Better UX */
#next-question-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

#next-question-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: scale(0.98);
}

#next-question-btn:not(:disabled) {
  opacity: 1;
  cursor: pointer;
  transform: scale(1);
}

#next-question-btn.enabled {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  animation: enabledPulse 2s ease-in-out;
}

#next-question-btn.disabled {
  background: #cccccc;
  box-shadow: none;
}

@keyframes enabledPulse {
  0% { box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3); }
  50% { box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5); }
  100% { box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3); }
}

/* Interactive Question Visual Feedback */
.drag-drop-matching .drop-zone.filled {
  background: linear-gradient(135deg, #e8f5e8, #d4edda);
  border-color: #4CAF50;
  animation: dropSuccess 0.5s ease;
}

@keyframes dropSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.area-models .fraction-segment.shaded,
.area-models .geometric-shape.shaded {
  animation: shadeSuccess 0.3s ease;
}

@keyframes shadeSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.number-line-slider .number-line-handle {
  transition: all 0.2s ease;
}

.number-line-slider .number-line-handle:active {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}