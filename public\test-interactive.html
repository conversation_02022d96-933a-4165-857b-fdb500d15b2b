<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="style.css" />
    <title>Interactive Questions Test - Mathematics Assessment</title>
    <style>
        body {
            padding: 2rem;
            background: #f3f4f6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-title {
            color: #1f2937;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .test-question {
            margin-bottom: 2rem;
        }
        .question-text {
            font-size: 1.125rem;
            color: #374151;
            margin-bottom: 1rem;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 500;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background: #f3f4f6;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="test-title">Interactive Mathematics Questions Test</h1>
        <p>This page tests the interactive question types for the mathematics assessment.</p>

        <!-- Number Line Test -->
        <div class="test-section">
            <h2 class="test-title">Number Line Slider Test</h2>
            <div class="test-question">
                <div class="question-text">Place the fraction 1/2 on the number line.</div>
                
                <!-- Number Line Slider -->
                <div id="number-line-slider" class="interactive-question">
                    <div class="number-line-container">
                        <div class="number-line-labels" id="number-line-labels"></div>
                        <div class="number-line-track" id="number-line-track">
                            <div class="number-line-handle" id="number-line-handle" tabindex="0" role="slider" aria-label="Number line position"></div>
                        </div>
                        <div class="number-line-value" id="number-line-value">0</div>
                    </div>
                    <p class="input-hint">Drag the handle to select your answer</p>
                </div>
                
                <button class="test-button" onclick="testNumberLine()">Test Number Line</button>
                <button class="test-button" onclick="getNumberLineAnswer()">Get Answer</button>
                <div id="number-line-result" class="test-result" style="display: none;"></div>
            </div>
        </div>

        <!-- Drag and Drop Test -->
        <div class="test-section">
            <h2 class="test-title">Drag and Drop Matching Test</h2>
            <div class="test-question">
                <div class="question-text">Match each fraction to its decimal equivalent.</div>
                
                <!-- Drag and Drop Matching -->
                <div id="drag-drop-matching" class="interactive-question">
                    <div class="matching-container">
                        <div class="draggable-items" id="draggable-items"></div>
                        <div class="drop-zones" id="drop-zones"></div>
                    </div>
                    <div class="interactive-controls">
                        <button id="reset-drag-drop-btn" class="reset-btn" type="button">
                            <span class="btn-icon">↺</span>
                            <span class="btn-text">Reset</span>
                        </button>
                    </div>
                    <p class="input-hint">Drag items to their correct positions</p>
                </div>
                
                <button class="test-button" onclick="testDragDrop()">Test Drag & Drop</button>
                <button class="test-button" onclick="getDragDropAnswer()">Get Answer</button>
                <button class="test-button" onclick="testResetDragDrop()">Test Reset</button>
                <div id="drag-drop-result" class="test-result" style="display: none;"></div>
            </div>
        </div>

        <!-- Additional Number Line Test -->
        <div class="test-section">
            <h2 class="test-title">Percentage Number Line Test</h2>
            <div class="test-question">
                <div class="question-text">Place 25% on the number line (as a decimal).</div>

                <!-- Number Line Slider for Percentages -->
                <div id="percentage-number-line" class="interactive-question">
                    <div class="number-line-container">
                        <div class="number-line-labels" id="percentage-line-labels"></div>
                        <div class="number-line-track" id="percentage-line-track">
                            <div class="number-line-handle" id="percentage-line-handle" tabindex="0" role="slider" aria-label="Percentage position"></div>
                        </div>
                        <div class="number-line-value" id="percentage-line-value">0</div>
                    </div>
                    <p class="input-hint">Drag the handle to place 25% as a decimal (0.25)</p>
                </div>

                <button class="test-button" onclick="testPercentageNumberLine()">Test Percentage Line</button>
                <button class="test-button" onclick="getPercentageAnswer()">Get Answer</button>
                <div id="percentage-result" class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="mathAssessment.js"></script>
    <script>
        // Create a test instance of MathAssessment
        const testAssessment = new MathAssessment();
        
        // Test Number Line
        function testNumberLine() {
            const question = {
                type: "number-line",
                numberLineConfig: {
                    min: 0,
                    max: 1,
                    step: 0.1,
                    snapToGrid: true
                }
            };
            testAssessment.showNumberLineSlider(question);
            document.getElementById('number-line-result').style.display = 'none';
        }
        
        function getNumberLineAnswer() {
            const answer = testAssessment.currentNumberLineValue?.toString() || 'No answer';
            document.getElementById('number-line-result').textContent = `Answer: ${answer}`;
            document.getElementById('number-line-result').style.display = 'block';
        }
        
        // Test Drag and Drop
        function testDragDrop() {
            const question = {
                type: "drag-drop",
                dragDropConfig: {
                    items: [
                        { id: "frac1", text: "1/4" },
                        { id: "frac2", text: "1/2" },
                        { id: "frac3", text: "3/4" }
                    ],
                    zones: [
                        { id: "dec1", label: "0.25" },
                        { id: "dec2", label: "0.50" },
                        { id: "dec3", label: "0.75" }
                    ]
                }
            };
            testAssessment.showDragDropMatching(question);
            document.getElementById('drag-drop-result').style.display = 'none';
        }
        
        function getDragDropAnswer() {
            const answer = testAssessment.getDragDropAnswer();
            document.getElementById('drag-drop-result').textContent = `Answer: ${answer}`;
            document.getElementById('drag-drop-result').style.display = 'block';
        }

        function testResetDragDrop() {
            testAssessment.resetDragDropQuestion();
            document.getElementById('drag-drop-result').textContent = 'Drag and drop question reset!';
            document.getElementById('drag-drop-result').style.display = 'block';
        }
        
        // Test Percentage Number Line
        function testPercentageNumberLine() {
            const question = {
                type: "number-line",
                numberLineConfig: {
                    min: 0,
                    max: 1,
                    step: 0.05,
                    snapToGrid: true
                }
            };

            // Use the percentage number line elements
            const originalContainer = document.getElementById('number-line-slider');
            const percentageContainer = document.getElementById('percentage-number-line');

            // Temporarily swap containers for testing
            originalContainer.classList.add('hidden');
            percentageContainer.classList.remove('hidden');

            // Set up the percentage number line
            testAssessment.setupPercentageNumberLine(question);
            document.getElementById('percentage-result').style.display = 'none';
        }

        function getPercentageAnswer() {
            const answer = testAssessment.currentNumberLineValue?.toString() || 'No answer';
            document.getElementById('percentage-result').textContent = `Answer: ${answer} (Target: 0.25 for 25%)`;
            document.getElementById('percentage-result').style.display = 'block';
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Interactive Questions Test Page Loaded');
        });
    </script>
</body>
</html>
