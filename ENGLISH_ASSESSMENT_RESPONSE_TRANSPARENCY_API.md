# English Assessment Response Transparency API Documentation

## Overview
This document provides comprehensive instructions for the frontend dashboard team on accessing and displaying the enhanced English assessment response data. The system now captures complete transparency of all student interactions during the assessment process.

## Database Schema Changes

### Enhanced User Document Structure
```javascript
// companies/{companyId}/users/{userEmail}
{
  // Existing fields...
  englishProficiencyScore: number,
  englishProficiencyLevel: string,
  englishAssessmentCompleted: boolean,
  
  // NEW: Comprehensive Response Transparency Data
  englishAssessmentResponses: {
    preliminaryQuestions: [
      {
        questionNumber: number,
        questionType: "grammar" | "vocabulary" | "sentence",
        questionText: string,
        questionContent: string,
        correctAnswer: string,
        studentResponse: string,
        timestamp: timestamp,
        timeSpent: number, // seconds
        questionStartTime: timestamp,
        responseEndTime: timestamp
      }
    ],
    essayResponse: {
      prompt: string,
      response: string,
      wordCount: number,
      characterCount: number,
      timestamp: timestamp,
      timeSpent: number, // seconds
      submissionType: "manual" | "timeout",
      essayStartTime: timestamp,
      submissionTime: timestamp
    },
    assessmentMetadata: {
      startTime: timestamp,
      endTime: timestamp,
      totalDuration: number, // seconds
      browserInfo: string,
      userAgent: string,
      screenResolution: string,
      timezone: string,
      language: string
    }
  }
}
```

## Admin Dashboard API Endpoints

### 1. Get Detailed Responses for Specific User
```javascript
GET /api/admin/english-responses/:email?company=Birmingham

// Response
{
  "success": true,
  "data": {
    "userEmail": "<EMAIL>",
    "name": "John Doe",
    "studentLevel": "college",
    "assessmentCompleted": true,
    "score": 15,
    "level": "L1",
    "completedAt": "2024-01-15T10:30:00Z",
    "detailedResponses": {
      "preliminaryQuestions": [...],
      "essayResponse": {...},
      "assessmentMetadata": {...}
    },
    "feedback": {...},
    "strengths": [...],
    "improvements": [...],
    "courseRecommendations": {...}
  }
}
```

### 2. Get All Assessment Responses with Filtering
```javascript
GET /api/admin/english-responses?company=Birmingham&level=L1&limit=50&offset=0

// Query Parameters:
// - company: string (default: "Birmingham")
// - level: string (filter by proficiency level)
// - completedAfter: ISO date string
// - completedBefore: ISO date string
// - limit: number (default: 50)
// - offset: number (default: 0)

// Response
{
  "success": true,
  "data": [
    {
      "userEmail": "<EMAIL>",
      "name": "John Doe",
      "studentLevel": "college",
      "score": 15,
      "level": "L1",
      "completedAt": "2024-01-15T10:30:00Z",
      "hasDetailedResponses": true,
      "preliminaryQuestionsCount": 4,
      "essayWordCount": 150,
      "totalDuration": 1800
    }
  ],
  "pagination": {
    "limit": 50,
    "offset": 0,
    "total": 25
  }
}
```

### 3. Get Assessment Analytics
```javascript
GET /api/admin/english-analytics?company=Birmingham

// Response
{
  "success": true,
  "data": {
    "totalAssessments": 150,
    "levelDistribution": {
      "Entry Level": 70,
      "L1": 50,
      "L2/GCSE": 30
    },
    "averageScores": {
      "overall": 12.5
    },
    "averageDuration": 1650, // seconds
    "responseQuality": {
      "withDetailedResponses": 150,
      "averageEssayWordCount": 125,
      "averagePreliminaryQuestions": 3.8
    }
  }
}
```

### 4. Export Assessment Data
```javascript
GET /api/admin/english-responses/export?company=Birmingham&format=json
GET /api/admin/english-responses/export?company=Birmingham&format=csv

// JSON Response: Complete data with detailed responses
// CSV Response: Downloadable CSV file with summary data
```

## Frontend Implementation Examples

### 1. Fetch User's Detailed Assessment Data
```javascript
async function fetchUserDetailedAssessment(userEmail) {
  try {
    const response = await fetch(`/api/admin/english-responses/${userEmail}?company=Birmingham`);
    const data = await response.json();
    
    if (data.success) {
      return data.data;
    } else {
      throw new Error('Failed to fetch user assessment data');
    }
  } catch (error) {
    console.error('Error fetching detailed assessment:', error);
    throw error;
  }
}
```

### 2. Display Preliminary Questions Responses
```javascript
function renderPreliminaryQuestions(preliminaryQuestions) {
  return preliminaryQuestions.map((q, index) => (
    <div key={index} className="question-response-card">
      <div className="question-header">
        <h4>Question {q.questionNumber}: {q.questionType}</h4>
        <span className="time-spent">{q.timeSpent}s</span>
      </div>
      
      <div className="question-content">
        <p><strong>Question:</strong> {q.questionText}</p>
        <p><strong>Content:</strong> "{q.questionContent}"</p>
        <p><strong>Expected Answer:</strong> "{q.correctAnswer}"</p>
        <p><strong>Student Response:</strong> "{q.studentResponse}"</p>
      </div>
      
      <div className="question-metadata">
        <small>Answered at: {new Date(q.timestamp.toDate()).toLocaleString()}</small>
      </div>
    </div>
  ));
}
```

### 3. Display Essay Response Details
```javascript
function renderEssayResponse(essayResponse) {
  return (
    <div className="essay-response-card">
      <div className="essay-header">
        <h4>Essay Response</h4>
        <div className="essay-stats">
          <span>Words: {essayResponse.wordCount}</span>
          <span>Characters: {essayResponse.characterCount}</span>
          <span>Time: {Math.floor(essayResponse.timeSpent / 60)}m {essayResponse.timeSpent % 60}s</span>
          <span>Submission: {essayResponse.submissionType}</span>
        </div>
      </div>
      
      <div className="essay-content">
        <p><strong>Prompt:</strong> {essayResponse.prompt}</p>
        <div className="essay-text">
          <strong>Student Response:</strong>
          <div className="response-text">{essayResponse.response}</div>
        </div>
      </div>
      
      <div className="essay-metadata">
        <small>
          Started: {new Date(essayResponse.essayStartTime.toDate()).toLocaleString()} | 
          Submitted: {new Date(essayResponse.submissionTime.toDate()).toLocaleString()}
        </small>
      </div>
    </div>
  );
}
```

### 4. Assessment Metadata Display
```javascript
function renderAssessmentMetadata(metadata) {
  return (
    <div className="metadata-card">
      <h4>Assessment Metadata</h4>
      <div className="metadata-grid">
        <div className="metadata-item">
          <label>Total Duration:</label>
          <span>{Math.floor(metadata.totalDuration / 60)}m {metadata.totalDuration % 60}s</span>
        </div>
        <div className="metadata-item">
          <label>Browser:</label>
          <span>{metadata.browserInfo}</span>
        </div>
        <div className="metadata-item">
          <label>Screen Resolution:</label>
          <span>{metadata.screenResolution}</span>
        </div>
        <div className="metadata-item">
          <label>Timezone:</label>
          <span>{metadata.timezone}</span>
        </div>
        <div className="metadata-item">
          <label>Language:</label>
          <span>{metadata.language}</span>
        </div>
        <div className="metadata-item">
          <label>Started:</label>
          <span>{new Date(metadata.startTime.toDate()).toLocaleString()}</span>
        </div>
        <div className="metadata-item">
          <label>Completed:</label>
          <span>{new Date(metadata.endTime.toDate()).toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
}
```

### 5. Complete Assessment Review Component
```javascript
function AssessmentReviewPage({ userEmail }) {
  const [assessmentData, setAssessmentData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadAssessmentData() {
      try {
        const data = await fetchUserDetailedAssessment(userEmail);
        setAssessmentData(data);
      } catch (error) {
        console.error('Failed to load assessment data:', error);
      } finally {
        setLoading(false);
      }
    }

    loadAssessmentData();
  }, [userEmail]);

  if (loading) return <div>Loading assessment details...</div>;
  if (!assessmentData) return <div>Assessment data not found</div>;

  const { detailedResponses } = assessmentData;

  return (
    <div className="assessment-review">
      <div className="assessment-header">
        <h2>Assessment Review: {assessmentData.name}</h2>
        <div className="assessment-summary">
          <span>Score: {assessmentData.score}/21</span>
          <span>Level: {assessmentData.level}</span>
          <span>Completed: {new Date(assessmentData.completedAt.toDate()).toLocaleDateString()}</span>
        </div>
      </div>

      {detailedResponses && (
        <>
          <section className="metadata-section">
            <h3>Assessment Overview</h3>
            {renderAssessmentMetadata(detailedResponses.assessmentMetadata)}
          </section>

          <section className="preliminary-section">
            <h3>Preliminary Questions ({detailedResponses.preliminaryQuestions.length})</h3>
            {renderPreliminaryQuestions(detailedResponses.preliminaryQuestions)}
          </section>

          <section className="essay-section">
            <h3>Essay Response</h3>
            {renderEssayResponse(detailedResponses.essayResponse)}
          </section>
        </>
      )}

      <section className="analysis-section">
        <h3>AI Analysis Results</h3>
        {/* Render existing feedback, strengths, improvements, etc. */}
      </section>
    </div>
  );
}
```

## Data Export and Analysis

### CSV Export Structure
The CSV export includes the following columns:
- userEmail, name, studentLevel, score, level, completedAt
- totalDuration, browserInfo
- essayWordCount, essayCharacterCount, essayTimeSpent, submissionType
- preliminaryQuestionsCount

### JSON Export Structure
The JSON export includes complete detailed responses for comprehensive analysis.

## Security Considerations

1. **Authentication**: Ensure proper admin authentication before accessing these endpoints
2. **Authorization**: Verify admin permissions for the specific company
3. **Data Privacy**: Implement appropriate data handling policies for student responses
4. **Rate Limiting**: Apply rate limiting to prevent abuse of export endpoints

## Use Cases for Manual Review

1. **Quality Assurance**: Compare AI scoring with manual review of responses
2. **Pattern Analysis**: Identify common response patterns and assessment effectiveness
3. **Question Improvement**: Analyze question performance and student understanding
4. **Instructor Feedback**: Provide detailed insights to instructors about student progress
5. **Audit Trail**: Maintain complete assessment integrity records

This comprehensive response transparency system enables thorough manual review and quality assurance while maintaining student privacy and data security.
