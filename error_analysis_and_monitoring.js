/**
 * Error Analysis and Monitoring System for Mathematics Assessment
 * This script provides comprehensive error tracking and analysis tools
 */

const fs = require('fs');
const path = require('path');

// Error pattern analysis
const ERROR_PATTERNS = {
  JSON_PARSING: {
    patterns: [
      /Unexpected token '£'/,
      /Unexpected token '"'/,
      /Expected ',' or '}'/,
      /Unexpected token '''/,
      /is not valid JSON/
    ],
    category: 'JSON_FORMATTING',
    severity: 'HIGH',
    solutions: [
      'Enhanced JSON cleaning function',
      'Currency symbol escaping',
      'Quote normalization',
      'Trailing comma removal'
    ]
  },
  API_TIMEOUT: {
    patterns: [
      /OpenAI API timeout after \d+ms/,
      /timeout after \d+ms \(threshold: \d+ms\)/
    ],
    category: 'API_PERFORMANCE',
    severity: 'MEDIUM',
    solutions: [
      'Increase timeout threshold',
      'Reduce concurrent requests',
      'Implement request queuing',
      'Optimize prompt length'
    ]
  },
  CACHE_ISSUES: {
    patterns: [
      /Cache size: 0/,
      /Cache miss - generating new questions/,
      /Failed to preload/
    ],
    category: 'CACHING',
    severity: 'MEDIUM',
    solutions: [
      'Fix cache storage logic',
      'Ensure fallback questions are cached',
      'Sequential preloading',
      'Better error handling'
    ]
  }
};

// Log analysis functions
class ErrorAnalyzer {
  constructor() {
    this.errorCounts = {};
    this.timelineData = [];
    this.performanceMetrics = {
      totalRequests: 0,
      successRate: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      errorsByCategory: {}
    };
  }

  /**
   * Analyze log content for error patterns
   */
  analyzeLogContent(logContent) {
    const lines = logContent.split('\n');
    const results = {
      totalLines: lines.length,
      errorsByPattern: {},
      timeline: [],
      recommendations: []
    };

    lines.forEach((line, index) => {
      const timestamp = this.extractTimestamp(line);
      
      // Check each error pattern
      Object.entries(ERROR_PATTERNS).forEach(([errorType, config]) => {
        config.patterns.forEach(pattern => {
          if (pattern.test(line)) {
            if (!results.errorsByPattern[errorType]) {
              results.errorsByPattern[errorType] = {
                count: 0,
                category: config.category,
                severity: config.severity,
                solutions: config.solutions,
                examples: []
              };
            }
            
            results.errorsByPattern[errorType].count++;
            
            // Store example (limit to 3 examples per error type)
            if (results.errorsByPattern[errorType].examples.length < 3) {
              results.errorsByPattern[errorType].examples.push({
                line: index + 1,
                content: line.trim(),
                timestamp
              });
            }
          }
        });
      });
    });

    // Generate recommendations based on error analysis
    results.recommendations = this.generateRecommendations(results.errorsByPattern);
    
    return results;
  }

  /**
   * Extract timestamp from log line
   */
  extractTimestamp(line) {
    const timestampMatch = line.match(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/);
    return timestampMatch ? timestampMatch[1] : null;
  }

  /**
   * Generate recommendations based on error patterns
   */
  generateRecommendations(errorsByPattern) {
    const recommendations = [];
    
    // JSON parsing errors
    if (errorsByPattern.JSON_PARSING && errorsByPattern.JSON_PARSING.count > 0) {
      recommendations.push({
        priority: 'HIGH',
        category: 'JSON_FORMATTING',
        issue: `${errorsByPattern.JSON_PARSING.count} JSON parsing errors detected`,
        solutions: [
          'Implement enhanced JSON cleaning function',
          'Add currency symbol escaping (£ → \\u00A3)',
          'Normalize quotes (single → double)',
          'Remove trailing commas',
          'Add more robust parsing strategies'
        ],
        impact: 'Reduces question generation success rate significantly'
      });
    }

    // API timeout errors
    if (errorsByPattern.API_TIMEOUT && errorsByPattern.API_TIMEOUT.count > 0) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'API_PERFORMANCE',
        issue: `${errorsByPattern.API_TIMEOUT.count} API timeout errors detected`,
        solutions: [
          'Increase timeout from 25s to 35s for enhanced prompts',
          'Implement sequential preloading instead of concurrent',
          'Add delays between API requests',
          'Optimize prompt token usage',
          'Implement request queuing'
        ],
        impact: 'Forces fallback to static questions, reducing variety'
      });
    }

    // Cache issues
    if (errorsByPattern.CACHE_ISSUES && errorsByPattern.CACHE_ISSUES.count > 0) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'CACHING',
        issue: `${errorsByPattern.CACHE_ISSUES.count} cache-related issues detected`,
        solutions: [
          'Ensure fallback questions are cached',
          'Fix cache storage logic',
          'Add cache size monitoring',
          'Implement cache persistence',
          'Better error handling in preloading'
        ],
        impact: 'Increases API calls and reduces performance'
      });
    }

    return recommendations;
  }

  /**
   * Generate comprehensive error report
   */
  generateErrorReport(analysisResults) {
    const report = {
      summary: {
        totalLines: analysisResults.totalLines,
        totalErrors: Object.values(analysisResults.errorsByPattern).reduce((sum, error) => sum + error.count, 0),
        errorTypes: Object.keys(analysisResults.errorsByPattern).length,
        analysisDate: new Date().toISOString()
      },
      errorBreakdown: analysisResults.errorsByPattern,
      recommendations: analysisResults.recommendations,
      actionPlan: this.generateActionPlan(analysisResults.recommendations)
    };

    return report;
  }

  /**
   * Generate prioritized action plan
   */
  generateActionPlan(recommendations) {
    const actionPlan = {
      immediate: [],
      shortTerm: [],
      longTerm: []
    };

    recommendations.forEach(rec => {
      const action = {
        task: rec.issue,
        solutions: rec.solutions,
        impact: rec.impact,
        category: rec.category
      };

      switch (rec.priority) {
        case 'HIGH':
          actionPlan.immediate.push(action);
          break;
        case 'MEDIUM':
          actionPlan.shortTerm.push(action);
          break;
        case 'LOW':
          actionPlan.longTerm.push(action);
          break;
      }
    });

    return actionPlan;
  }

  /**
   * Monitor real-time performance metrics
   */
  async monitorPerformanceMetrics(baseUrl = 'http://localhost:3000') {
    try {
      const response = await fetch(`${baseUrl}/api/math-assessments/performance`);
      if (!response.ok) {
        throw new Error(`Performance API failed: ${response.status}`);
      }

      const data = await response.json();
      const metrics = data.metrics;

      console.log('\n📊 Real-time Performance Metrics:');
      console.log('=====================================');
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Cache Hit Rate: ${((metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100).toFixed(1)}%`);
      console.log(`Success Rate: ${((metrics.aiGenerationSuccess / (metrics.aiGenerationSuccess + metrics.aiGenerationFailures)) * 100).toFixed(1)}%`);
      console.log(`JSON Parsing Success Rate: ${((metrics.jsonParsingSuccess / (metrics.jsonParsingSuccess + metrics.jsonParsingErrors)) * 100).toFixed(1)}%`);
      console.log(`Fallback Usage: ${metrics.fallbackUsage} (${((metrics.fallbackUsage / metrics.totalRequests) * 100).toFixed(1)}%)`);
      console.log(`API Timeouts: ${metrics.apiTimeouts}`);
      console.log(`Average Generation Time: ${metrics.averageGenerationTime.toFixed(0)}ms`);

      return metrics;
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error.message);
      return null;
    }
  }
}

/**
 * Main analysis function
 */
async function analyzeErrorLogs(logFilePath) {
  console.log('🔍 Mathematics Assessment Error Analysis');
  console.log('========================================\n');

  const analyzer = new ErrorAnalyzer();

  try {
    // Read log content
    let logContent;
    if (logFilePath && fs.existsSync(logFilePath)) {
      logContent = fs.readFileSync(logFilePath, 'utf8');
      console.log(`📁 Analyzing log file: ${logFilePath}`);
    } else {
      // Use sample log content from the provided logs
      logContent = `
OpenAI API Key: Set
Server is running on http://localhost:3000
Error parsing AI questions response: SyntaxError: Unexpected token '£', ..."", "£50", £51", £52""... is not valid JSON
Error parsing AI questions response: SyntaxError: Unexpected token '"', ..." ["1/12", "1/4", "1/"... is not valid JSON
⏰ OpenAI API timeout after 25004ms (threshold: 25000ms) - using fallback questions
⏰ OpenAI API timeout after 25141ms (threshold: 25000ms) - using fallback questions
🎯 Cache preloading completed. Cache size: 0
Error parsing AI questions response: SyntaxError: Unexpected token ''', ..."tAnswer": '12', "... is not valid JSON
Expected 7 questions, got 0
AI generated invalid questions, using enhanced fallback
      `;
      console.log('📝 Analyzing sample log content');
    }

    // Analyze the logs
    const analysisResults = analyzer.analyzeLogContent(logContent);
    const errorReport = analyzer.generateErrorReport(analysisResults);

    // Display results
    console.log('📊 Error Analysis Results:');
    console.log(`Total log lines analyzed: ${errorReport.summary.totalLines}`);
    console.log(`Total errors found: ${errorReport.summary.totalErrors}`);
    console.log(`Error types: ${errorReport.summary.errorTypes}\n`);

    // Display error breakdown
    console.log('🔴 Error Breakdown:');
    Object.entries(errorReport.errorBreakdown).forEach(([errorType, details]) => {
      console.log(`\n${errorType} (${details.category}):`);
      console.log(`  Count: ${details.count}`);
      console.log(`  Severity: ${details.severity}`);
      console.log(`  Examples:`);
      details.examples.forEach((example, index) => {
        console.log(`    ${index + 1}. Line ${example.line}: ${example.content.substring(0, 80)}...`);
      });
    });

    // Display recommendations
    console.log('\n💡 Recommendations:');
    errorReport.recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. ${rec.issue} (${rec.priority} priority)`);
      console.log(`   Impact: ${rec.impact}`);
      console.log(`   Solutions:`);
      rec.solutions.forEach(solution => console.log(`     • ${solution}`));
    });

    // Display action plan
    console.log('\n📋 Action Plan:');
    if (errorReport.actionPlan.immediate.length > 0) {
      console.log('\n🚨 IMMEDIATE (High Priority):');
      errorReport.actionPlan.immediate.forEach((action, index) => {
        console.log(`${index + 1}. ${action.task}`);
        console.log(`   Primary solutions: ${action.solutions.slice(0, 2).join(', ')}`);
      });
    }

    if (errorReport.actionPlan.shortTerm.length > 0) {
      console.log('\n⏰ SHORT TERM (Medium Priority):');
      errorReport.actionPlan.shortTerm.forEach((action, index) => {
        console.log(`${index + 1}. ${action.task}`);
      });
    }

    // Monitor current performance if server is running
    console.log('\n🔄 Checking current performance metrics...');
    await analyzer.monitorPerformanceMetrics();

    console.log('\n✅ Error analysis completed!');
    
    return errorReport;

  } catch (error) {
    console.error('Error during analysis:', error);
    return null;
  }
}

// Export for use as module
module.exports = { ErrorAnalyzer, analyzeErrorLogs };

// Run analysis if called directly
if (require.main === module) {
  const logFilePath = process.argv[2]; // Optional log file path
  analyzeErrorLogs(logFilePath).catch(console.error);
}
