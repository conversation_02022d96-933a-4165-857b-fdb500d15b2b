/**
 * Test script for Question Deduplication and Number Line Configuration Fixes
 * This script validates both critical fixes implemented in the mathematics assessment system
 */

// Mock functions for testing
function createQuestionSignature(question) {
  const normalizedQuestion = question.question
    .toLowerCase()
    .replace(/\d+/g, 'NUM')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  return `${question.topic}_${question.type}_${normalizedQuestion}`;
}

function normalizeQuestionText(text) {
  return text
    .toLowerCase()
    .replace(/\d+(\.\d+)?/g, 'X')
    .replace(/[£$€¥]/g, 'CURRENCY')
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}

function calculateTextSimilarity(text1, text2) {
  const words1 = new Set(text1.split(' '));
  const words2 = new Set(text2.split(' '));
  
  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
}

function areQuestionsSimilar(question1, question2) {
  if (question1.topic === question2.topic && question1.type === question2.type) {
    const text1 = normalizeQuestionText(question1.question);
    const text2 = normalizeQuestionText(question2.question);
    
    const similarity = calculateTextSimilarity(text1, text2);
    return similarity > 0.7;
  }
  return false;
}

function ensureQuestionDiversity(questions, level) {
  console.log(`🔍 Analyzing question diversity for ${level} level...`);
  
  const uniqueQuestions = [];
  const seenQuestions = new Set();
  const topicCounts = {};
  const typeCounts = {};
  
  let duplicatesRemoved = 0;
  let similarQuestionsFound = 0;
  
  for (const question of questions) {
    const signature = createQuestionSignature(question);
    
    if (seenQuestions.has(signature)) {
      duplicatesRemoved++;
      console.log(`🚫 Removed duplicate question: "${question.question.substring(0, 50)}..."`);
      continue;
    }
    
    const isSimilar = uniqueQuestions.some(existing => 
      areQuestionsSimilar(question, existing)
    );
    
    if (isSimilar) {
      similarQuestionsFound++;
      console.log(`⚠️ Found similar question: "${question.question.substring(0, 50)}..."`);
      continue;
    }
    
    uniqueQuestions.push(question);
    seenQuestions.add(signature);
    topicCounts[question.topic] = (topicCounts[question.topic] || 0) + 1;
    typeCounts[question.type] = (typeCounts[question.type] || 0) + 1;
  }
  
  console.log(`📊 Question Diversity Analysis:`);
  console.log(`  Original questions: ${questions.length}`);
  console.log(`  Unique questions: ${uniqueQuestions.length}`);
  console.log(`  Duplicates removed: ${duplicatesRemoved}`);
  console.log(`  Similar questions found: ${similarQuestionsFound}`);
  console.log(`  Topic distribution:`, topicCounts);
  console.log(`  Type distribution:`, typeCounts);
  
  return uniqueQuestions;
}

function validateNumberLineQuestion(question) {
  const errors = [];
  let isValid = true;
  
  if (!question.numberLineConfig) {
    errors.push('Missing numberLineConfig');
    return { isValid: false, errors };
  }
  
  const config = question.numberLineConfig;
  const correctAnswer = parseFloat(question.correctAnswer);
  
  if (isNaN(correctAnswer)) {
    errors.push(`Invalid correct answer: ${question.correctAnswer}`);
    isValid = false;
  }
  
  if (correctAnswer < config.min || correctAnswer > config.max) {
    errors.push(`Correct answer ${correctAnswer} outside range [${config.min}, ${config.max}]`);
    isValid = false;
  }
  
  if (config.step && config.step > 0) {
    const stepsFromMin = (correctAnswer - config.min) / config.step;
    if (Math.abs(stepsFromMin - Math.round(stepsFromMin)) > 0.001) {
      errors.push(`Correct answer ${correctAnswer} not achievable with step ${config.step} from min ${config.min}`);
      isValid = false;
    }
  }
  
  if (question.question.toLowerCase().includes('decimal') || 
      question.question.toLowerCase().includes('%') ||
      correctAnswer % 1 !== 0) {
    
    if (config.step >= 1) {
      errors.push(`Decimal question requires step < 1, got step: ${config.step}`);
      isValid = false;
    }
  }
  
  if (question.question.toLowerCase().includes('%')) {
    if (config.max < 1 && correctAnswer > config.max) {
      errors.push(`Percentage question may need max >= 1 for decimal representation`);
      isValid = false;
    }
  }
  
  return { isValid, errors };
}

// Test data
const testQuestions = [
  {
    id: 1,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 25 + 37?",
    options: ["52", "62", "72", "82"],
    correctAnswer: "62",
    points: 2
  },
  {
    id: 2,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 25 + 37?", // Exact duplicate
    options: ["52", "62", "72", "82"],
    correctAnswer: "62",
    points: 2
  },
  {
    id: 3,
    type: "multiple-choice",
    topic: "arithmetic",
    question: "What is 26 + 38?", // Very similar
    options: ["54", "64", "74", "84"],
    correctAnswer: "64",
    points: 2
  },
  {
    id: 4,
    type: "numeric",
    topic: "fractions",
    question: "What is 1/2 + 1/4?",
    correctAnswer: "0.75",
    points: 2
  },
  {
    id: 5,
    type: "multiple-choice",
    topic: "geometry",
    question: "What is the area of a rectangle with length 5 and width 3?",
    options: ["8", "15", "16", "20"],
    correctAnswer: "15",
    points: 3
  }
];

const testNumberLineQuestions = [
  {
    id: 1,
    type: "number-line",
    topic: "percentages",
    question: "Place 25% on the number line (as a decimal).",
    numberLineConfig: {
      min: 0,
      max: 1,
      step: 0.05,
      snapToGrid: true
    },
    correctAnswer: "0.25",
    points: 2,
    explanation: "25% = 25/100 = 0.25"
  },
  {
    id: 2,
    type: "number-line",
    topic: "percentages",
    question: "Place 50% on the number line (as a decimal).",
    numberLineConfig: {
      min: 0,
      max: 1,
      step: 1, // This should fail validation - step too large for decimal
      snapToGrid: true
    },
    correctAnswer: "0.5",
    points: 2,
    explanation: "50% = 50/100 = 0.5"
  },
  {
    id: 3,
    type: "number-line",
    topic: "integers",
    question: "Place -3 on the number line.",
    numberLineConfig: {
      min: -5,
      max: 5,
      step: 1,
      snapToGrid: true
    },
    correctAnswer: "-3",
    points: 2,
    explanation: "-3 is three units to the left of zero."
  },
  {
    id: 4,
    type: "number-line",
    topic: "decimals",
    question: "Place 0.75 on the number line.",
    numberLineConfig: {
      min: 0,
      max: 2, // Answer outside range
      step: 0.05,
      snapToGrid: true
    },
    correctAnswer: "1.75", // This should fail - outside range
    points: 2,
    explanation: "0.75 is three-quarters of the way from 0 to 1."
  }
];

// Run tests
function runTests() {
  console.log('🧪 Testing Question Fixes for Mathematics Assessment');
  console.log('===================================================\n');
  
  // Test 1: Question Deduplication
  console.log('📝 Test 1: Question Deduplication');
  console.log('='.repeat(40));
  
  const originalCount = testQuestions.length;
  const uniqueQuestions = ensureQuestionDiversity(testQuestions, 'Entry');
  const finalCount = uniqueQuestions.length;
  
  console.log(`\n✅ Deduplication Results:`);
  console.log(`  Original questions: ${originalCount}`);
  console.log(`  After deduplication: ${finalCount}`);
  console.log(`  Questions removed: ${originalCount - finalCount}`);
  console.log(`  Success: ${finalCount < originalCount ? 'YES' : 'NO'}`);
  
  // Test 2: Number Line Validation
  console.log('\n📏 Test 2: Number Line Question Validation');
  console.log('='.repeat(45));
  
  let validQuestions = 0;
  let invalidQuestions = 0;
  
  testNumberLineQuestions.forEach((question, index) => {
    console.log(`\nValidating Question ${index + 1}: "${question.question.substring(0, 50)}..."`);
    
    const validation = validateNumberLineQuestion(question);
    
    if (validation.isValid) {
      console.log(`  ✅ VALID`);
      validQuestions++;
    } else {
      console.log(`  ❌ INVALID: ${validation.errors.join(', ')}`);
      invalidQuestions++;
    }
    
    console.log(`  Config: min=${question.numberLineConfig.min}, max=${question.numberLineConfig.max}, step=${question.numberLineConfig.step}`);
    console.log(`  Answer: ${question.correctAnswer}`);
  });
  
  console.log(`\n✅ Number Line Validation Results:`);
  console.log(`  Valid questions: ${validQuestions}`);
  console.log(`  Invalid questions: ${invalidQuestions}`);
  console.log(`  Success: ${invalidQuestions > 0 ? 'YES (caught invalid configs)' : 'All valid'}`);
  
  // Test 3: Specific 25% decimal question
  console.log('\n🎯 Test 3: Specific 25% Decimal Question');
  console.log('='.repeat(40));
  
  const percentageQuestion = testNumberLineQuestions[0];
  const percentageValidation = validateNumberLineQuestion(percentageQuestion);
  
  console.log(`Question: "${percentageQuestion.question}"`);
  console.log(`Config: step=${percentageQuestion.numberLineConfig.step}, range=[${percentageQuestion.numberLineConfig.min}, ${percentageQuestion.numberLineConfig.max}]`);
  console.log(`Answer: ${percentageQuestion.correctAnswer}`);
  console.log(`Validation: ${percentageValidation.isValid ? '✅ VALID' : '❌ INVALID'}`);
  
  if (!percentageValidation.isValid) {
    console.log(`Errors: ${percentageValidation.errors.join(', ')}`);
  }
  
  // Calculate if 0.25 can be placed with step 0.05
  const stepsFromMin = (0.25 - 0) / 0.05;
  console.log(`Steps from min to answer: ${stepsFromMin} (should be whole number: ${stepsFromMin % 1 === 0 ? 'YES' : 'NO'})`);
  
  console.log('\n📋 Overall Test Summary');
  console.log('========================');
  console.log(`✅ Question deduplication: ${finalCount < originalCount ? 'WORKING' : 'NEEDS REVIEW'}`);
  console.log(`✅ Number line validation: ${invalidQuestions > 0 ? 'WORKING' : 'ALL VALID'}`);
  console.log(`✅ 25% decimal question: ${percentageValidation.isValid ? 'WORKING' : 'NEEDS FIX'}`);
  
  console.log('\n🎉 Question fixes testing completed!');
}

// Run the tests
runTests();
